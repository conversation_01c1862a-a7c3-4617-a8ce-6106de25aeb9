<script setup lang="ts">
import { ref, watch } from "vue";
import type { EnergyRule } from "@/api/energy-rules";

interface Props {
  modelValue: boolean;
  data?: EnergyRule | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};

// 格式化时间
const formatTime = (time?: string) => {
  return time ? new Date(time).toLocaleString() : "-";
};

// 获取规则类型标签
const getRuleTypeLabel = (ruleType: string) => {
  const typeMap = {
    'meditation_complete': '冥想完成',
    'daily_login': '每日登录',
    'continuous_days': '连续天数',
    'special_event': '特殊事件',
    'achievement': '成就解锁',
    'level_up': '等级提升'
  };
  return typeMap[ruleType] || ruleType;
};

// 格式化条件
const formatCondition = (condition: any) => {
  const conditions = [];
  
  if (condition.min_duration) {
    conditions.push(`最少时长: ${Math.floor(condition.min_duration / 60)}分钟`);
  }
  if (condition.meditation_type) {
    conditions.push(`冥想类型: ${condition.meditation_type}`);
  }
  if (condition.event_id) {
    conditions.push(`事件ID: ${condition.event_id}`);
  }
  if (condition.start_date && condition.end_date) {
    conditions.push(`时间范围: ${condition.start_date} ~ ${condition.end_date}`);
  }
  
  return conditions.length > 0 ? conditions : ['无特殊条件'];
};
</script>

<template>
  <el-dialog
    v-model="visible"
    title="奖励规则详情"
    width="800px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="规则名称">
          {{ data.rule_name }}
        </el-descriptions-item>
        <el-descriptions-item label="规则类型">
          <el-tag type="primary" size="small">
            {{ getRuleTypeLabel(data.rule_type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="能量奖励">
          {{ data.energy_amount }}
        </el-descriptions-item>
        <el-descriptions-item label="奖励倍数">
          {{ data.bonus_multiplier }}x
        </el-descriptions-item>
        <el-descriptions-item label="每日限制次数">
          {{ data.max_daily_times > 0 ? `${data.max_daily_times}次` : '无限制' }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag 
            :type="data.priority >= 15 ? 'danger' : data.priority >= 10 ? 'warning' : 'success'" 
            size="small"
          >
            {{ data.priority }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.is_active ? 'success' : 'danger'" size="small">
            {{ data.is_active ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(data.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatTime(data.updated_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="触发条件" :span="2">
          <div class="condition-list">
            <div 
              v-for="(condition, index) in formatCondition(data.condition)" 
              :key="index"
              class="condition-item"
            >
              <el-tag size="small" type="info">{{ condition }}</el-tag>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="规则描述" :span="2">
          {{ data.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}

.condition-list {
  .condition-item {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
