<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";
import EditLevelDialog from "./modules/EditLevelDialog.vue";
import {
  getUserList,
  updateUserLevel as updateUserLevelApi,
  getUserStatistics,
  type User as ApiUser,
  type UserListParams
} from "@/api/users";
import { getUserLevelList, type UserLevel } from "@/api/userLevels";

defineOptions({
  name: "UserList"
});

interface User {
  id: string;
  avatar_url: string;
  nickname: string;
  openid: string;
  meditation_level: number;
  levelName: string;
  streak_days: number;
  plant_count: number;
  created_at: string;
  updated_at?: string;
}

const loading = ref(false);
const tableData = ref<User[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});
const detailDialogVisible = ref(false);
const editLevelDialogVisible = ref(false);
const currentRow = ref<User | null>(null);
const userLevels = ref<UserLevel[]>([]);

const searchForm = reactive({
  search: "",
  meditation_level: "",
  start_date: "",
  end_date: ""
});

// 获取等级名称
const getLevelName = (level: number) => {
  const userLevel = userLevels.value.find(item => item.level === level);
  return userLevel ? userLevel.level_name : `等级${level}`;
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return "-";
  return new Date(dateStr).toLocaleString("zh-CN");
};

const columns = [
  {
    label: "用户头像",
    slot: "avatar",
    minWidth: 80
  },
  {
    label: "昵称",
    prop: "nickname",
    minWidth: 120
  },
  {
    label: "OpenID",
    prop: "openid",
    minWidth: 150
  },
  {
    label: "冥想等级",
    slot: "level",
    minWidth: 100
  },
  {
    label: "连续天数",
    prop: "streak_days",
    minWidth: 100
  },
  {
    label: "多肉数量",
    prop: "plant_count",
    minWidth: 100
  },
  {
    label: "注册时间",
    slot: "created_at",
    minWidth: 160
  },
  {
    label: "更新时间",
    slot: "updated_at",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
];

// 加载用户等级配置
const loadUserLevels = async () => {
  try {
    const response = await getUserLevelList();
    if (response.code === 200) {
      userLevels.value = response.data;
    }
  } catch (error) {
    console.error("加载用户等级配置失败:", error);
  }
};

const onSearch = async () => {
  loading.value = true;
  try {
    const params: UserListParams = {
      page: pagination.currentPage,
      limit: pagination.pageSize
    };

    if (searchForm.search) params.search = searchForm.search;
    if (searchForm.meditation_level)
      params.meditation_level = Number(searchForm.meditation_level);
    if (searchForm.start_date) params.start_date = searchForm.start_date;
    if (searchForm.end_date) params.end_date = searchForm.end_date;

    const response = await getUserList(params);
    if (response.code === 200) {
      tableData.value = response.data.items.map(item => ({
        ...item,
        levelName: getLevelName(item.meditation_level)
      }));
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.message || "获取用户列表失败");
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
    ElMessage.error("获取用户列表失败");
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  searchForm.search = "";
  searchForm.meditation_level = "";
  searchForm.start_date = "";
  searchForm.end_date = "";
  pagination.currentPage = 1;
  onSearch();
};

const handleEditLevel = (row: User) => {
  currentRow.value = row;
  editLevelDialogVisible.value = true;
};

// 处理等级更新成功
const handleLevelUpdated = () => {
  onSearch(); // 刷新列表
};

const handleViewDetail = (row: User) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  onSearch();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  onSearch();
};

onMounted(async () => {
  await loadUserLevels();
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="搜索：" prop="search">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入用户昵称"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="等级：" prop="meditation_level">
        <el-select
          v-model="searchForm.meditation_level"
          placeholder="请选择等级"
          clearable
          class="!w-[180px]"
        >
          <el-option
            v-for="item in userLevels"
            :key="item.level"
            :label="item.level_name"
            :value="item.level"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始日期：" prop="start_date">
        <el-date-picker
          v-model="searchForm.start_date"
          type="date"
          placeholder="选择开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="结束日期：" prop="end_date">
        <el-date-picker
          v-model="searchForm.end_date"
          type="date"
          placeholder="选择结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="用户列表" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button type="primary" :icon="useRenderIcon('ep:download')">
          导出数据
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #avatar="{ row }">
            <el-avatar :size="50" :src="row.avatar_url" />
          </template>

          <template #level="{ row }">
            <el-tag type="primary">
              {{ row.meditation_level }}级 -
              {{ getLevelName(row.meditation_level) }}
            </el-tag>
          </template>

          <template #created_at="{ row }">
            {{ formatDate(row.created_at) }}
          </template>

          <template #updated_at="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEditLevel(row)"
            >
              编辑等级
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />

    <!-- 编辑等级对话框 -->
    <EditLevelDialog
      v-model="editLevelDialogVisible"
      :user="currentRow"
      :levels="userLevels"
      @success="handleLevelUpdated"
    />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
