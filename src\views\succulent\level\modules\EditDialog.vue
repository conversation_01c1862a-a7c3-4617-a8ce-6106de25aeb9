<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  createSucculentLevel,
  updateSucculentLevel,
  type SucculentLevel,
  type CreateSucculentLevelParams,
  type UpdateSucculentLevelParams
} from "@/api/succulent-levels";

interface Props {
  modelValue: boolean;
  data?: SucculentLevel | null;
  isEdit: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "save"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? "编辑多肉等级" : "新增多肉等级";
});

// 表单数据
const formData = reactive({
  level: 1,
  name: "",
  icon: "",
  required_energy: 0,
  required_days: 0,
  attribute_bonus: {
    growth_speed: 1,
    energy_efficiency: 1,
    resistance: 1,
    beauty: 1,
    magic: 1
  },
  special_ability: {
    abilities: [] as string[]
  },
  unlock_reward: {
    coins: 0,
    experience: 0,
    items: [] as string[],
    special_title: ""
  },
  description: "",
  sort_order: 1
});

// 表单验证规则
const rules: FormRules = {
  level: [
    { required: true, message: "请输入等级数值", trigger: "blur" },
    {
      type: "number",
      min: 1,
      max: 100,
      message: "等级数值必须在1-100之间",
      trigger: "blur"
    }
  ],
  name: [
    { required: true, message: "请输入等级名称", trigger: "blur" },
    { min: 1, max: 20, message: "等级名称长度在1-20个字符", trigger: "blur" }
  ],
  icon: [{ required: true, message: "请输入等级图标", trigger: "blur" }],
  required_energy: [
    { required: true, message: "请输入所需能量", trigger: "blur" },
    { type: "number", min: 0, message: "所需能量不能小于0", trigger: "blur" }
  ],
  required_days: [
    { required: true, message: "请输入所需天数", trigger: "blur" },
    { type: "number", min: 0, message: "所需天数不能小于0", trigger: "blur" }
  ],
  description: [
    { required: true, message: "请输入等级描述", trigger: "blur" },
    { max: 200, message: "描述长度不能超过200个字符", trigger: "blur" }
  ]
};

// 特殊能力选项
const abilityOptions = [
  "Basic Growth",
  "Fast Growth",
  "Basic Photosynthesis",
  "Efficient Photosynthesis",
  "Perfect Photosynthesis",
  "Legendary Photosynthesis",
  "Environmental Adaptation",
  "Enhanced Adaptation",
  "Perfect Adaptation",
  "Self Repair",
  "Quick Repair",
  "Instant Repair",
  "Environment Beautification",
  "Environment Purification",
  "Aura Emission"
];

// 奖励道具选项
const itemOptions = [
  "fertilizer_basic",
  "fertilizer_advanced",
  "fertilizer_premium",
  "fertilizer_legendary",
  "fertilizer_mythical",
  "decoration_pot",
  "decoration_premium",
  "decoration_legendary",
  "decoration_mythical",
  "seed_rare",
  "seed_legendary",
  "seed_mythical"
];

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    level: 1,
    name: "",
    icon: "",
    required_energy: 0,
    required_days: 0,
    attribute_bonus: {
      growth_speed: 1,
      energy_efficiency: 1,
      resistance: 1,
      beauty: 1,
      magic: 1
    },
    special_ability: {
      abilities: []
    },
    unlock_reward: {
      coins: 0,
      experience: 0,
      items: [],
      special_title: ""
    },
    description: "",
    sort_order: 1
  });
};

// 填充表单数据
const fillFormData = (data: SucculentLevel) => {
  Object.assign(formData, {
    level: data.level,
    name: data.name,
    icon: data.icon,
    required_energy: data.required_energy,
    required_days: data.required_days,
    attribute_bonus: { ...data.attribute_bonus },
    special_ability: {
      abilities: [...data.special_ability.abilities]
    },
    unlock_reward: {
      ...data.unlock_reward,
      items: data.unlock_reward.items ? [...data.unlock_reward.items] : []
    },
    description: data.description,
    sort_order: data.sort_order
  });
};

// 监听对话框显示状态
watch(visible, val => {
  if (val) {
    if (props.isEdit && props.data) {
      fillFormData(props.data);
    } else {
      resetFormData();
    }
  } else {
    formRef.value?.resetFields();
  }
});

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    if (props.isEdit && props.data) {
      // 编辑
      const updateData: UpdateSucculentLevelParams = {
        name: formData.name,
        icon: formData.icon,
        required_energy: formData.required_energy,
        required_days: formData.required_days,
        attribute_bonus: formData.attribute_bonus,
        special_ability: formData.special_ability,
        unlock_reward: formData.unlock_reward,
        description: formData.description,
        sort_order: formData.sort_order
      };

      const response = await updateSucculentLevel(props.data.id, updateData);
      if (response.code === 200) {
        ElMessage.success("编辑成功");
        emit("save");
        visible.value = false;
      } else {
        ElMessage.error("编辑失败");
      }
    } else {
      // 新增
      const createData: CreateSucculentLevelParams = {
        level: formData.level,
        name: formData.name,
        icon: formData.icon,
        required_energy: formData.required_energy,
        required_days: formData.required_days,
        attribute_bonus: formData.attribute_bonus,
        special_ability: formData.special_ability,
        unlock_reward: formData.unlock_reward,
        description: formData.description,
        sort_order: formData.sort_order
      };

      const response = await createSucculentLevel(createData);
      if (response.code === 200) {
        ElMessage.success("新增成功");
        emit("save");
        visible.value = false;
      } else {
        ElMessage.error("新增失败");
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  visible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    draggable
    destroy-on-close
    @close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="等级数值" prop="level">
            <el-input-number
              v-model="formData.level"
              :min="1"
              :max="100"
              :disabled="isEdit"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="等级名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入等级名称"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="等级图标" prop="icon">
            <el-input v-model="formData.icon" placeholder="请输入图标URL" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort_order">
            <el-input-number
              v-model="formData.sort_order"
              :min="1"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所需能量" prop="required_energy">
            <el-input-number
              v-model="formData.required_energy"
              :min="0"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所需天数" prop="required_days">
            <el-input-number
              v-model="formData.required_days"
              :min="0"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 属性加成 -->
      <el-form-item label="属性加成">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="attribute-item">
              <div class="attribute-label">
                成长速度: {{ formData.attribute_bonus.growth_speed }}x
              </div>
              <el-slider
                v-model="formData.attribute_bonus.growth_speed"
                :min="1"
                :max="5"
                :step="0.1"
                show-stops
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="attribute-item">
              <div class="attribute-label">
                能量效率: {{ formData.attribute_bonus.energy_efficiency }}x
              </div>
              <el-slider
                v-model="formData.attribute_bonus.energy_efficiency"
                :min="1"
                :max="5"
                :step="0.1"
                show-stops
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="attribute-item">
              <div class="attribute-label">
                抗性: {{ formData.attribute_bonus.resistance }}x
              </div>
              <el-slider
                v-model="formData.attribute_bonus.resistance"
                :min="1"
                :max="5"
                :step="0.1"
                show-stops
              />
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="attribute-item">
              <div class="attribute-label">
                美观: {{ formData.attribute_bonus.beauty }}x
              </div>
              <el-slider
                v-model="formData.attribute_bonus.beauty"
                :min="1"
                :max="5"
                :step="0.1"
                show-stops
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="attribute-item">
              <div class="attribute-label">
                魔力: {{ formData.attribute_bonus.magic }}x
              </div>
              <el-slider
                v-model="formData.attribute_bonus.magic"
                :min="1"
                :max="5"
                :step="0.1"
                show-stops
              />
            </div>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 特殊能力 -->
      <el-form-item label="特殊能力">
        <el-select
          v-model="formData.special_ability.abilities"
          multiple
          placeholder="请选择特殊能力"
          style="width: 100%"
        >
          <el-option
            v-for="ability in abilityOptions"
            :key="ability"
            :label="ability"
            :value="ability"
          />
        </el-select>
      </el-form-item>

      <!-- 解锁奖励 -->
      <el-form-item label="解锁奖励">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="金币奖励">
              <el-input-number
                v-model="formData.unlock_reward.coins"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经验奖励">
              <el-input-number
                v-model="formData.unlock_reward.experience"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="特殊称号">
              <el-input
                v-model="formData.unlock_reward.special_title"
                placeholder="请输入特殊称号"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="奖励道具">
          <el-select
            v-model="formData.unlock_reward.items"
            multiple
            placeholder="请选择奖励道具"
            style="width: 100%"
          >
            <el-option
              v-for="item in itemOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </el-form-item>

      <!-- 等级描述 -->
      <el-form-item label="等级描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入等级描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.attribute-item {
  margin-bottom: 16px;

  .attribute-label {
    font-weight: 500;
    margin-bottom: 8px;
  }
}
</style>
