<script setup lang="ts">
import { ref, reactive, onMounted, h } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";
import EditDialog from "./modules/EditDialog.vue";
import {
  getEnergyRuleList,
  deleteEnergyRule,
  toggleEnergyRuleStatus,
  type EnergyRule,
  RULE_TYPE_OPTIONS,
  PRIORITY_OPTIONS
} from "@/api/energy-rules";

defineOptions({
  name: "SucculentReward"
});

const loading = ref(false);
const tableData = ref<EnergyRule[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

// 搜索表单
const searchForm = reactive({
  rule_type: "",
  is_active: undefined as boolean | undefined
});

const detailDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentRow = ref<EnergyRule | null>(null);
const isEdit = ref(false);

// 状态选项
const statusOptions = [
  { label: "启用", value: true },
  { label: "禁用", value: false }
];

// 获取规则类型标签
const getRuleTypeLabel = (ruleType: string) => {
  const option = RULE_TYPE_OPTIONS.find(opt => opt.value === ruleType);
  return option ? option.label : ruleType;
};

// 获取优先级标签和类型
const getPriorityInfo = (priority: number) => {
  if (priority >= 20) return { type: "danger", text: "最高" };
  if (priority >= 15) return { type: "warning", text: "高" };
  if (priority >= 10) return { type: "primary", text: "中" };
  if (priority >= 5) return { type: "success", text: "低" };
  return { type: "info", text: "最低" };
};

// 表格列配置
const columns: TableColumnList = [
  {
    label: "规则名称",
    prop: "rule_name",
    minWidth: 150
  },
  {
    label: "规则类型",
    prop: "rule_type",
    minWidth: 120,
    cellRenderer: ({ row }) => getRuleTypeLabel(row.rule_type)
  },
  {
    label: "触发条件",
    prop: "condition",
    minWidth: 200,
    cellRenderer: ({ row }) => {
      const condition = row.condition;
      const conditionTexts = [];

      if (condition.min_duration) {
        conditionTexts.push(
          `最少时长: ${Math.floor(condition.min_duration / 60)}分钟`
        );
      }
      if (condition.meditation_type) {
        conditionTexts.push(`冥想类型: ${condition.meditation_type}`);
      }
      if (condition.event_id) {
        conditionTexts.push(`事件ID: ${condition.event_id}`);
      }
      if (condition.start_date && condition.end_date) {
        conditionTexts.push(
          `时间范围: ${condition.start_date} ~ ${condition.end_date}`
        );
      }

      return h(
        "div",
        { class: "text-sm" },
        conditionTexts.length > 0
          ? conditionTexts.map(text => h("div", text))
          : "无特殊条件"
      );
    }
  },
  {
    label: "奖励内容",
    prop: "reward",
    minWidth: 180,
    cellRenderer: ({ row }) => {
      return h(
        "div",
        { class: "text-sm" },
        [
          h("div", `能量: ${row.energy_amount}`),
          h("div", `倍数: ${row.bonus_multiplier}x`),
          row.max_daily_times > 0 &&
            h("div", `每日限制: ${row.max_daily_times}次`)
        ].filter(Boolean)
      );
    }
  },
  {
    label: "优先级",
    prop: "priority",
    minWidth: 80,
    cellRenderer: ({ row }) => {
      const info = getPriorityInfo(row.priority);
      return h(
        "el-tag",
        {
          type: info.type,
          size: "small"
        },
        info.text
      );
    }
  },
  {
    label: "状态",
    prop: "is_active",
    minWidth: 80,
    cellRenderer: ({ row }) => {
      return h(
        "el-tag",
        {
          type: row.is_active ? "success" : "danger",
          size: "small"
        },
        row.is_active ? "启用" : "禁用"
      );
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 240,
    slot: "operation"
  }
];

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getEnergyRuleList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      rule_type: searchForm.rule_type || undefined,
      is_active: searchForm.is_active
    });

    if (response.code === 200) {
      tableData.value = response.data.list;
      pagination.total = response.data.pagination.total;
    } else {
      ElMessage.error("获取奖励规则列表失败");
    }
  } catch (error) {
    console.error("获取奖励规则列表失败:", error);
    ElMessage.error("获取奖励规则列表失败");
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  loadData();
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  loadData();
};

// 重置搜索
const resetForm = () => {
  searchForm.rule_type = "";
  searchForm.is_active = undefined;
  onSearch();
};

// 新增规则
const handleAdd = () => {
  isEdit.value = false;
  currentRow.value = null;
  editDialogVisible.value = true;
};

// 查看详情
const handleDetail = (row: EnergyRule) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

// 编辑规则
const handleEdit = (row: EnergyRule) => {
  isEdit.value = true;
  currentRow.value = row;
  editDialogVisible.value = true;
};

// 删除规则
const handleDelete = async (row: EnergyRule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除奖励规则 ${row.rule_name} 吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deleteEnergyRule(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      loadData();
    } else {
      ElMessage.error("删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除奖励规则失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 切换状态
const handleToggleStatus = async (row: EnergyRule) => {
  const action = row.is_active ? "禁用" : "启用";
  try {
    await ElMessageBox.confirm(
      `确定要${action}奖励规则 ${row.rule_name} 吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await toggleEnergyRuleStatus(row.id);
    if (response.code === 200) {
      ElMessage.success(`${action}成功`);
      loadData();
    } else {
      ElMessage.error(`${action}失败`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("切换状态失败:", error);
      ElMessage.error(`${action}失败`);
    }
  }
};

// 处理编辑对话框的保存事件
const handleEditSave = () => {
  loadData();
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="规则类型：" prop="rule_type">
        <el-select
          v-model="searchForm.rule_type"
          placeholder="请选择规则类型"
          clearable
          class="!w-[180px]"
        >
          <el-option
            v-for="item in RULE_TYPE_OPTIONS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="is_active">
        <el-select
          v-model="searchForm.is_active"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="奖励机制管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增奖励规则
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row, size }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.is_active ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.is_active ? 'ep:lock' : 'ep:unlock')"
              @click="handleToggleStatus(row)"
            >
              {{ row.is_active ? "禁用" : "启用" }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />

    <!-- 新增/编辑对话框 -->
    <EditDialog
      v-model="editDialogVisible"
      :data="currentRow"
      :is-edit="isEdit"
      @save="handleEditSave"
    />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
