const Layout = () => import("@/layout/index.vue");

export default {
  path: "/meditation",
  name: "Meditation",
  component: Layout,
  redirect: "/meditation/content",
  meta: {
    icon: "ep/headset",
    title: "冥想内容管理",
    rank: 2
  },
  children: [
    {
      path: "/meditation/content",
      name: "MeditationContent",
      component: () => import("@/views/meditation/content/index.vue"),
      meta: {
        title: "内容管理",
        showLink: true
      }
    },
    {
      path: "/meditation/category",
      name: "MeditationCategory",
      component: () => import("@/views/meditation/category/index.vue"),
      meta: {
        title: "分类管理",
        showLink: true
      }
    },
    {
      path: "/meditation/tag",
      name: "MeditationTag",
      component: () => import("@/views/meditation/tag/index.vue"),
      meta: {
        title: "标签管理",
        showLink: true
      }
    },
    {
      path: "/meditation/course",
      name: "MeditationCourse",
      component: () => import("@/views/meditation/course/index.vue"),
      meta: {
        title: "课程管理",
        showLink: true
      }
    },
    {
      path: "/meditation/course/:id/chapters",
      name: "CourseChapters",
      component: () => import("@/views/meditation/course/chapters.vue"),
      meta: {
        title: "章节管理",
        showLink: false
      }
    },
    {
      path: "/meditation/course/create",
      name: "CreateCourse",
      component: () => import("@/views/meditation/course/form.vue"),
      meta: {
        title: "创建课程",
        showLink: false
      }
    },
    {
      path: "/meditation/course/:id/edit",
      name: "EditCourse",
      component: () => import("@/views/meditation/course/form.vue"),
      meta: {
        title: "编辑课程",
        showLink: false
      }
    }
  ]
} satisfies RouteConfigsTable;
