import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 成长记录数据类型定义
export type GrowthRecord = {
  id: string;
  openid: string;
  unionid: string;
  nickname: string;
  avatar_url: string;
  meditation_level: number;
  streak_days: number;
  created_at: string;
  updated_at?: string;
  plants: Plant[];
  meditation_stats: MeditationStat[];
  growth_days: number;
  total_energy: number;
  avg_plant_level: number;
  health_status: "healthy" | "unhealthy" | "excellent";
  last_care_time: string;
  total_meditation_duration: number;
  total_energy_gained: number;
  achievements: Achievement[];
  plant_count: number;
};

// 多肉植物数据类型定义
export type Plant = {
  id: string;
  user_id: string;
  species: string;
  energy_value: number;
  level: number;
  created_at: string;
  updated_at?: string;
  growth_records: PlantGrowthRecord[];
};

// 植物成长记录数据类型定义
export type PlantGrowthRecord = {
  id: string;
  plant_id: string;
  change_value: number;
  reason: string;
  created_at: string;
};

// 冥想统计数据类型定义
export type MeditationStat = {
  id: string;
  user_id: string;
  date: string;
  duration: number;
  session_count: number;
  energy_gained: number;
  created_at: string;
};

// 成就数据类型定义
export type Achievement = {
  id: string;
  user_id: string;
  achievement_type: string;
  achievement_name: string;
  description: string;
  unlocked_at: string;
};

// 成长记录列表响应类型
export type GrowthRecordListResponse = {
  code: number;
  message?: string;
  data: {
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    items: GrowthRecord[];
  };
};

// 成长记录详情响应类型
export type GrowthRecordResponse = {
  code: number;
  message?: string;
  data: GrowthRecord;
};

// 统计数据响应类型
export type GrowthRecordStatisticsResponse = {
  code: number;
  message?: string;
  data: {
    total_users: number;
    active_users: number;
    total_plants: number;
    avg_plant_level: number;
    total_energy: number;
    total_meditation_time: number;
    level_distribution: Record<string, number>;
    plant_level_distribution: Record<string, number>;
    health_status_distribution: Record<string, number>;
  };
};

// 通用操作响应类型
export type OperationResponse = {
  code: number;
  message?: string;
  data?: any;
};

// 成长记录列表查询参数
export type GrowthRecordListParams = {
  page?: number;
  limit?: number;
  search?: string;
  level?: number;
  plant_level?: number;
};

// 更新用户等级参数
export type UpdateUserLevelParams = {
  level: number;
  reason: string;
};

// 更新多肉能量值参数
export type UpdatePlantEnergyParams = {
  energy_change: number;
  reason: string;
};

// ==================== 成长记录管理接口 ====================

/** 获取成长记录列表 */
export const getGrowthRecordList = (params?: GrowthRecordListParams) => {
  return http.request<GrowthRecordListResponse>(
    "get",
    baseUrlApi("admin/growth-records"),
    {
      params
    }
  );
};

/** 获取用户成长记录详情 */
export const getGrowthRecordDetail = (id: string) => {
  return http.request<GrowthRecordResponse>(
    "get",
    baseUrlApi(`admin/growth-records/${id}`)
  );
};

/** 更新用户等级 */
export const updateUserLevel = (id: string, data: UpdateUserLevelParams) => {
  return http.request<OperationResponse>(
    "put",
    baseUrlApi(`admin/growth-records/${id}/update-level`),
    {
      data
    }
  );
};

/** 更新多肉能量值 */
export const updatePlantEnergy = (
  userId: string,
  plantId: string,
  data: UpdatePlantEnergyParams
) => {
  return http.request<OperationResponse>(
    "put",
    baseUrlApi(`admin/growth-records/${userId}/plants/${plantId}/update-energy`),
    {
      data
    }
  );
};

/** 获取成长记录统计数据 */
export const getGrowthRecordStatistics = () => {
  return http.request<GrowthRecordStatisticsResponse>(
    "get",
    baseUrlApi("admin/growth-records/statistics")
  );
};
