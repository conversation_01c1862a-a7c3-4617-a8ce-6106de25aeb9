<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getUserStatistics, type UserStatisticsResponse } from "@/api/users";

defineOptions({
  name: "UserStatistics"
});

const loading = ref(false);
const statistics = ref<UserStatisticsResponse['data'] | null>(null);

// 加载统计数据
const loadStatistics = async () => {
  loading.value = true;
  try {
    const response = await getUserStatistics();
    if (response.code === 200) {
      statistics.value = response.data;
    }
  } catch (error) {
    console.error('获取用户统计数据失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadStatistics();
});
</script>

<template>
  <div class="main">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    <div v-else-if="statistics">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="mb-6">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total_users }}</div>
              <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-icon">
              <el-icon size="40" color="#409EFF">
                <User />
              </el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.active_users }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-icon">
              <el-icon size="40" color="#67C23A">
                <UserFilled />
              </el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.recent_registrations.length }}</div>
              <div class="stat-label">今日新增</div>
            </div>
            <div class="stat-icon">
              <el-icon size="40" color="#E6A23C">
                <Plus />
              </el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.level_distribution.length }}</div>
              <div class="stat-label">等级分布</div>
            </div>
            <div class="stat-icon">
              <el-icon size="40" color="#F56C6C">
                <TrendCharts />
              </el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 等级分布 -->
      <el-card class="mb-6">
        <template #header>
          <div class="card-header">
            <span>用户等级分布</span>
          </div>
        </template>
        <el-table :data="statistics.level_distribution" stripe>
          <el-table-column prop="meditation_level" label="冥想等级" width="120">
            <template #default="{ row }">
              <el-tag type="primary">等级{{ row.meditation_level }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="用户数量" width="120">
            <template #default="{ row }">
              <span class="font-bold text-lg">{{ row.count }}</span>
            </template>
          </el-table-column>
          <el-table-column label="占比">
            <template #default="{ row }">
              <el-progress 
                :percentage="Math.round((Number(row.count) / statistics.total_users) * 100)"
                :stroke-width="20"
                :show-text="true"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 最近注册用户 -->
      <el-card v-if="statistics.recent_registrations.length > 0">
        <template #header>
          <div class="card-header">
            <span>最近注册用户</span>
          </div>
        </template>
        <el-table :data="statistics.recent_registrations" stripe>
          <el-table-column label="头像" width="80">
            <template #default="{ row }">
              <el-avatar :size="40" :src="row.avatar_url" />
            </template>
          </el-table-column>
          <el-table-column prop="nickname" label="昵称" width="120" />
          <el-table-column prop="meditation_level" label="等级" width="100">
            <template #default="{ row }">
              <el-tag type="primary">等级{{ row.meditation_level }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="streak_days" label="连续天数" width="100" />
          <el-table-column prop="created_at" label="注册时间">
            <template #default="{ row }">
              {{ new Date(row.created_at).toLocaleString('zh-CN') }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.stat-card {
  .el-card__body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
  }
}

.stat-content {
  .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #303133;
    line-height: 1;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #909399;
  }
}

.stat-icon {
  opacity: 0.8;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.loading-container {
  padding: 20px;
}
</style>
