<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";
import {
  type PlantSpecies,
  type PlantSpeciesListParams,
  type CreatePlantSpeciesParams,
  type UpdatePlantSpeciesParams,
  getPlantSpeciesList,
  createPlantSpecies,
  updatePlantSpecies,
  deletePlantSpecies
} from "@/api/plants";

defineOptions({
  name: "SucculentVariety"
});

const loading = ref(false);
const tableData = ref<PlantSpecies[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);
const detailDialogVisible = ref(false);
const currentRow = ref<PlantSpecies | null>(null);

// 分页参数
const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
});

const formData = reactive({
  id: "",
  name: "",
  display_name: "",
  description: "",
  rarity: "common" as "common" | "rare" | "epic" | "legendary",
  unlock_condition: {
    level: 1,
    days: 0,
    special_achievement: ""
  },
  growth_stages: {
    stages: [
      { level: 1, name: "幼苗期" },
      { level: 5, name: "成长期" },
      { level: 10, name: "成熟期" }
    ]
  },
  max_level: 10,
  base_energy_per_level: 100,
  image_urls: {} as Record<string, string>,
  is_active: true
});

const searchForm = reactive({
  search: "",
  rarity: "" as "" | "common" | "rare" | "epic" | "legendary"
});

const rarityOptions = [
  { label: "普通", value: "common" },
  { label: "稀有", value: "rare" },
  { label: "史诗", value: "epic" },
  { label: "传说", value: "legendary" }
];

const rarityLabels = {
  common: "普通",
  rare: "稀有",
  epic: "史诗",
  legendary: "传说"
};

const statusOptions = [
  { label: "启用", value: true },
  { label: "禁用", value: false }
];

const columns = [
  {
    label: "图片",
    prop: "image_urls",
    minWidth: 80,
    slot: "image"
  },
  {
    label: "显示名称",
    prop: "display_name",
    minWidth: 120
  },
  {
    label: "内部名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "稀有度",
    prop: "rarity",
    minWidth: 100,
    slot: "rarity"
  },
  {
    label: "最大等级",
    prop: "max_level",
    minWidth: 100
  },
  {
    label: "基础能量",
    prop: "base_energy_per_level",
    minWidth: 100
  },
  {
    label: "解锁条件",
    prop: "unlock_condition",
    minWidth: 150,
    slot: "unlock_condition"
  },
  {
    label: "状态",
    prop: "is_active",
    minWidth: 80,
    slot: "status"
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 150,
    slot: "created_at"
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
];

// 获取品种列表
const getList = async () => {
  try {
    loading.value = true;
    const params: PlantSpeciesListParams = {
      page: pagination.page,
      limit: pagination.limit,
      search: searchForm.search || undefined,
      rarity: searchForm.rarity || undefined
    };

    const response = await getPlantSpeciesList(params);
    if (response.code === 200) {
      tableData.value = response.data.items;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.message || "获取品种列表失败");
    }
  } catch (error) {
    console.error("获取品种列表失败:", error);
    ElMessage.error("获取品种列表失败");
  } finally {
    loading.value = false;
  }
};

const onSearch = () => {
  pagination.page = 1;
  getList();
};

const resetForm = () => {
  searchForm.search = "";
  searchForm.rarity = "";
  onSearch();
};

const handleAdd = () => {
  dialogTitle.value = "新增品种";
  isEdit.value = false;
  resetFormData();
  dialogVisible.value = true;
};

const handleDetail = (row: PlantSpecies) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

const handleEdit = (row: PlantSpecies) => {
  dialogTitle.value = "编辑品种";
  isEdit.value = true;
  Object.assign(formData, {
    ...row,
    unlock_condition: { ...row.unlock_condition },
    growth_stages: {
      stages: [...row.growth_stages.stages]
    },
    image_urls: { ...row.image_urls }
  });
  dialogVisible.value = true;
};

const handleDelete = async (row: PlantSpecies) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除品种 ${row.display_name} 吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deletePlantSpecies(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      getList();
    } else {
      ElMessage.error(response.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除品种失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

const handleToggleStatus = async (row: PlantSpecies) => {
  const action = row.is_active ? "禁用" : "启用";
  try {
    await ElMessageBox.confirm(
      `确定要${action}品种 ${row.display_name} 吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await updatePlantSpecies(row.id, {
      is_active: !row.is_active
    });

    if (response.code === 200) {
      ElMessage.success(`${action}成功`);
      getList();
    } else {
      ElMessage.error(response.message || `${action}失败`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error(`${action}品种失败:`, error);
      ElMessage.error(`${action}失败`);
    }
  }
};

const resetFormData = () => {
  formData.id = "";
  formData.name = "";
  formData.display_name = "";
  formData.description = "";
  formData.rarity = "common";
  formData.unlock_condition = {
    level: 1,
    days: 0,
    special_achievement: ""
  };
  formData.growth_stages = {
    stages: [
      { level: 1, name: "幼苗期" },
      { level: 5, name: "成长期" },
      { level: 10, name: "成熟期" }
    ]
  };
  formData.max_level = 10;
  formData.base_energy_per_level = 100;
  formData.image_urls = {};
  formData.is_active = true;
};

const handleSubmit = async () => {
  if (!formData.name) {
    ElMessage.warning("请输入内部名称");
    return;
  }

  if (!formData.display_name) {
    ElMessage.warning("请输入显示名称");
    return;
  }

  try {
    if (isEdit.value) {
      const updateData: UpdatePlantSpeciesParams = {
        display_name: formData.display_name,
        description: formData.description,
        rarity: formData.rarity,
        unlock_condition: formData.unlock_condition,
        growth_stages: formData.growth_stages,
        max_level: formData.max_level,
        base_energy_per_level: formData.base_energy_per_level,
        image_urls: formData.image_urls,
        is_active: formData.is_active
      };

      const response = await updatePlantSpecies(formData.id, updateData);
      if (response.code === 200) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        getList();
      } else {
        ElMessage.error(response.message || "编辑失败");
      }
    } else {
      const createData: CreatePlantSpeciesParams = {
        name: formData.name,
        display_name: formData.display_name,
        description: formData.description,
        rarity: formData.rarity,
        unlock_condition: formData.unlock_condition,
        growth_stages: formData.growth_stages,
        max_level: formData.max_level,
        base_energy_per_level: formData.base_energy_per_level,
        image_urls: formData.image_urls
      };

      const response = await createPlantSpecies(createData);
      if (response.code === 200) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        getList();
      } else {
        ElMessage.error(response.message || "新增失败");
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

const getRarityTag = (rarity: "common" | "rare" | "epic" | "legendary") => {
  const rarityMap = {
    common: { type: "info", color: "#909399" },
    rare: { type: "success", color: "#67C23A" },
    epic: { type: "warning", color: "#E6A23C" },
    legendary: { type: "danger", color: "#F56C6C" }
  };
  return rarityMap[rarity] || { type: "info", color: "#909399" };
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("zh-CN");
};

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="搜索：" prop="search">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入品种名称或描述"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="稀有度：" prop="rarity">
        <el-select
          v-model="searchForm.rarity"
          placeholder="请选择稀有度"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in rarityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="多肉品种管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增品种
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #image="{ row }">
            <el-image
              v-if="row.image_urls && Object.keys(row.image_urls).length > 0"
              :src="Object.values(row.image_urls)[0]"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 8px"
              :preview-src-list="Object.values(row.image_urls)"
              preview-teleported
            />
            <div
              v-else
              class="w-[60px] h-[60px] bg-gray-100 rounded-lg flex items-center justify-center text-gray-400"
            >
              暂无图片
            </div>
          </template>

          <template #rarity="{ row }">
            <el-tag :type="getRarityTag(row.rarity).type">
              {{ rarityLabels[row.rarity] }}
            </el-tag>
          </template>

          <template #unlock_condition="{ row }">
            <div class="text-sm">
              <div>等级: {{ row.unlock_condition.level }}</div>
              <div>天数: {{ row.unlock_condition.days }}</div>
              <div v-if="row.unlock_condition.special_achievement">
                成就: {{ row.unlock_condition.special_achievement }}
              </div>
            </div>
          </template>

          <template #status="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? "启用" : "禁用" }}
            </el-tag>
          </template>

          <template #created_at="{ row }">
            {{ formatDate(row.created_at) }}
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.is_active ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.is_active ? 'ep:close' : 'ep:check')"
              @click="handleToggleStatus(row)"
            >
              {{ row.is_active ? "禁用" : "启用" }}
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 分页 -->
    <div class="flex justify-end mt-4">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      draggable
    >
      <el-form :model="formData" label-width="120px" label-position="right">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="内部名称" required>
              <el-input
                v-model="formData.name"
                placeholder="请输入内部名称（英文）"
                maxlength="50"
                show-word-limit
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示名称" required>
              <el-input
                v-model="formData.display_name"
                placeholder="请输入显示名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="稀有度">
          <el-select
            v-model="formData.rarity"
            placeholder="请选择稀有度"
            style="width: 200px"
          >
            <el-option
              v-for="item in rarityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入品种描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大等级">
              <el-input-number
                v-model="formData.max_level"
                :min="1"
                :max="100"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基础能量">
              <el-input-number
                v-model="formData.base_energy_per_level"
                :min="1"
                :max="1000"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="解锁条件">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="等级要求">
                <el-input-number
                  v-model="formData.unlock_condition.level"
                  :min="1"
                  :max="50"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="天数要求">
                <el-input-number
                  v-model="formData.unlock_condition.days"
                  :min="0"
                  :max="365"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="特殊成就">
                <el-input
                  v-model="formData.unlock_condition.special_achievement"
                  placeholder="可选"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="成长阶段">
          <div
            v-for="(stage, index) in formData.growth_stages.stages"
            :key="index"
            class="mb-2"
          >
            <el-row :gutter="10" align="middle">
              <el-col :span="8">
                <el-input-number
                  v-model="stage.level"
                  :min="1"
                  :max="formData.max_level"
                  controls-position="right"
                  style="width: 100%"
                  placeholder="等级"
                />
              </el-col>
              <el-col :span="12">
                <el-input
                  v-model="stage.name"
                  placeholder="阶段名称"
                  maxlength="20"
                />
              </el-col>
              <el-col :span="4">
                <el-button
                  v-if="formData.growth_stages.stages.length > 1"
                  type="danger"
                  size="small"
                  @click="formData.growth_stages.stages.splice(index, 1)"
                >
                  删除
                </el-button>
              </el-col>
            </el-row>
          </div>
          <el-button
            type="primary"
            size="small"
            @click="
              formData.growth_stages.stages.push({
                level: formData.max_level,
                name: ''
              })
            "
          >
            添加阶段
          </el-button>
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group v-model="formData.is_active">
            <el-radio :value="true">启用</el-radio>
            <el-radio :value="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
