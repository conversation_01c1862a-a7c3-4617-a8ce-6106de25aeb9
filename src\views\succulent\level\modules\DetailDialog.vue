<template>
  <el-dialog
    v-model="visible"
    title="多肉等级详情"
    width="900px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="等级">
          {{ data.level }}级
        </el-descriptions-item>
        <el-descriptions-item label="等级名称">
          {{ data.name }}
        </el-descriptions-item>
        <el-descriptions-item label="图标">
          <img
            :src="data.icon"
            :alt="data.name"
            style="
              width: 32px;
              height: 32px;
              object-fit: cover;
              border-radius: 4px;
            "
          />
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ data.sort_order }}
        </el-descriptions-item>
        <el-descriptions-item label="所需能量">
          {{ data.required_energy }}
        </el-descriptions-item>
        <el-descriptions-item label="所需天数">
          {{ data.required_days }}天
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.is_active ? 'success' : 'danger'">
            {{ data.is_active ? "启用" : "禁用" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(data.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatTime(data.updated_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="属性加成" :span="2">
          <div class="bonus-stats">
            <div class="bonus-item">
              <span class="bonus-label">成长速度:</span>
              <el-progress
                :percentage="(data.attribute_bonus.growth_speed - 1) * 25"
                :stroke-width="8"
                :show-text="false"
                color="#409eff"
              />
              <span class="bonus-value"
                >{{ data.attribute_bonus.growth_speed }}x</span
              >
            </div>
            <div class="bonus-item">
              <span class="bonus-label">能量效率:</span>
              <el-progress
                :percentage="(data.attribute_bonus.energy_efficiency - 1) * 25"
                :stroke-width="8"
                :show-text="false"
                color="#f56c6c"
              />
              <span class="bonus-value"
                >{{ data.attribute_bonus.energy_efficiency }}x</span
              >
            </div>
            <div v-if="data.attribute_bonus.resistance" class="bonus-item">
              <span class="bonus-label">抗性:</span>
              <el-progress
                :percentage="(data.attribute_bonus.resistance - 1) * 25"
                :stroke-width="8"
                :show-text="false"
                color="#67c23a"
              />
              <span class="bonus-value"
                >{{ data.attribute_bonus.resistance }}x</span
              >
            </div>
            <div v-if="data.attribute_bonus.beauty" class="bonus-item">
              <span class="bonus-label">美观:</span>
              <el-progress
                :percentage="(data.attribute_bonus.beauty - 1) * 20"
                :stroke-width="8"
                :show-text="false"
                color="#e6a23c"
              />
              <span class="bonus-value"
                >{{ data.attribute_bonus.beauty }}x</span
              >
            </div>
            <div v-if="data.attribute_bonus.magic" class="bonus-item">
              <span class="bonus-label">魔力:</span>
              <el-progress
                :percentage="(data.attribute_bonus.magic - 1) * 25"
                :stroke-width="8"
                :show-text="false"
                color="#9c27b0"
              />
              <span class="bonus-value">{{ data.attribute_bonus.magic }}x</span>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="特殊能力" :span="2">
          <div class="abilities">
            <el-tag
              v-for="ability in data.special_ability.abilities"
              :key="ability"
              style="margin-right: 8px; margin-bottom: 4px"
              type="info"
            >
              {{ ability }}
            </el-tag>
            <span
              v-if="
                !data.special_ability.abilities ||
                data.special_ability.abilities.length === 0
              "
            >
              暂无特殊能力
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="解锁奖励" :span="2">
          <div class="rewards">
            <div class="reward-section">
              <div class="reward-item">
                <span class="reward-label">金币:</span>
                <el-tag type="warning">{{ data.unlock_reward.coins }}</el-tag>
              </div>
              <div class="reward-item">
                <span class="reward-label">经验:</span>
                <el-tag type="success">{{
                  data.unlock_reward.experience
                }}</el-tag>
              </div>
              <div v-if="data.unlock_reward.special_title" class="reward-item">
                <span class="reward-label">特殊称号:</span>
                <el-tag type="danger">{{
                  data.unlock_reward.special_title
                }}</el-tag>
              </div>
            </div>
            <div
              v-if="
                data.unlock_reward.items && data.unlock_reward.items.length > 0
              "
              class="reward-items"
            >
              <div class="reward-label">奖励道具:</div>
              <el-tag
                v-for="item in data.unlock_reward.items"
                :key="item"
                style="margin-right: 8px; margin-bottom: 4px"
                type="primary"
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ data.description || "暂无描述" }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import type { SucculentLevel } from "@/api/succulent-levels";

interface Props {
  modelValue: boolean;
  data?: SucculentLevel | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, val => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};

// 格式化时间
const formatTime = (time?: string) => {
  return time ? new Date(time).toLocaleString() : "-";
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}

.bonus-stats {
  .bonus-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .bonus-label {
      width: 80px;
      font-weight: 500;
      margin-right: 12px;
    }

    :deep(.el-progress) {
      flex: 1;
      margin-right: 12px;
    }

    .bonus-value {
      font-weight: 600;
      color: var(--el-color-primary);
      min-width: 40px;
    }
  }
}

.abilities {
  line-height: 1.8;
}

.rewards {
  .reward-section {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;

    .reward-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .reward-label {
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
    }
  }

  .reward-items {
    .reward-label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
      display: block;
    }
  }
}
</style>
