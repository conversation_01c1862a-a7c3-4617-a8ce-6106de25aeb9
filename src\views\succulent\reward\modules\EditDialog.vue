<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  createEnergyRule,
  updateEnergyRule,
  type EnergyRule,
  type CreateEnergyRuleParams,
  type UpdateEnergyRuleParams,
  RULE_TYPE_OPTIONS,
  MEDITATION_TYPE_OPTIONS,
  PRIORITY_OPTIONS
} from "@/api/energy-rules";

interface Props {
  modelValue: boolean;
  data?: EnergyRule | null;
  isEdit: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "save"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const loading = ref(false);

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? "编辑奖励规则" : "新增奖励规则";
});

// 表单数据
const formData = reactive({
  rule_name: "",
  rule_type: "meditation_complete",
  condition: {
    min_duration: 0,
    meditation_type: "",
    event_id: "",
    start_date: "",
    end_date: ""
  },
  energy_amount: 10,
  bonus_multiplier: 1.0,
  max_daily_times: 1,
  description: "",
  priority: 10,
  is_active: true
});

// 表单验证规则
const rules: FormRules = {
  rule_name: [
    { required: true, message: "请输入规则名称", trigger: "blur" },
    { min: 1, max: 50, message: "规则名称长度在1-50个字符", trigger: "blur" }
  ],
  rule_type: [{ required: true, message: "请选择规则类型", trigger: "change" }],
  energy_amount: [
    { required: true, message: "请输入能量奖励", trigger: "blur" },
    { type: "number", min: 1, message: "能量奖励不能小于1", trigger: "blur" }
  ],
  bonus_multiplier: [
    { required: true, message: "请输入奖励倍数", trigger: "blur" },
    {
      type: "number",
      min: 0.1,
      message: "奖励倍数不能小于0.1",
      trigger: "blur"
    }
  ],
  max_daily_times: [
    { required: true, message: "请输入每日限制次数", trigger: "blur" },
    {
      type: "number",
      min: 0,
      message: "每日限制次数不能小于0",
      trigger: "blur"
    }
  ],
  priority: [{ required: true, message: "请选择优先级", trigger: "change" }],
  description: [
    { required: true, message: "请输入规则描述", trigger: "blur" },
    { max: 200, message: "描述长度不能超过200个字符", trigger: "blur" }
  ]
};

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    rule_name: "",
    rule_type: "meditation_complete",
    condition: {
      min_duration: 0,
      meditation_type: "",
      event_id: "",
      start_date: "",
      end_date: ""
    },
    energy_amount: 10,
    bonus_multiplier: 1.0,
    max_daily_times: 1,
    description: "",
    priority: 10,
    is_active: true
  });
};

// 填充表单数据
const fillFormData = (data: EnergyRule) => {
  Object.assign(formData, {
    rule_name: data.rule_name,
    rule_type: data.rule_type,
    condition: { ...data.condition },
    energy_amount: data.energy_amount,
    bonus_multiplier: data.bonus_multiplier,
    max_daily_times: data.max_daily_times,
    description: data.description,
    priority: data.priority,
    is_active: data.is_active
  });
};

// 监听对话框显示状态
watch(visible, val => {
  if (val) {
    if (props.isEdit && props.data) {
      fillFormData(props.data);
    } else {
      resetFormData();
    }
  } else {
    formRef.value?.resetFields();
  }
});

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    if (props.isEdit && props.data) {
      // 编辑
      const updateData: UpdateEnergyRuleParams = {
        rule_name: formData.rule_name,
        condition: formData.condition,
        energy_amount: formData.energy_amount,
        bonus_multiplier: formData.bonus_multiplier,
        max_daily_times: formData.max_daily_times,
        description: formData.description,
        priority: formData.priority
      };

      const response = await updateEnergyRule(props.data.id, updateData);
      if (response.code === 200) {
        ElMessage.success("编辑成功");
        emit("save");
        visible.value = false;
      } else {
        ElMessage.error("编辑失败");
      }
    } else {
      // 新增
      const createData: CreateEnergyRuleParams = {
        rule_name: formData.rule_name,
        rule_type: formData.rule_type,
        condition: formData.condition,
        energy_amount: formData.energy_amount,
        bonus_multiplier: formData.bonus_multiplier,
        max_daily_times: formData.max_daily_times,
        description: formData.description,
        priority: formData.priority,
        is_active: formData.is_active
      };

      const response = await createEnergyRule(createData);
      if (response.code === 200) {
        ElMessage.success("新增成功");
        emit("save");
        visible.value = false;
      } else {
        ElMessage.error("新增失败");
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  visible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    draggable
    destroy-on-close
    @close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规则名称" prop="rule_name">
            <el-input
              v-model="formData.rule_name"
              placeholder="请输入规则名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规则类型" prop="rule_type">
            <el-select
              v-model="formData.rule_type"
              placeholder="请选择规则类型"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="item in RULE_TYPE_OPTIONS"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="能量奖励" prop="energy_amount">
            <el-input-number
              v-model="formData.energy_amount"
              :min="1"
              :max="10000"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="奖励倍数" prop="bonus_multiplier">
            <el-input-number
              v-model="formData.bonus_multiplier"
              :min="0.1"
              :max="10"
              :step="0.1"
              :precision="1"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="每日限制次数" prop="max_daily_times">
            <el-input-number
              v-model="formData.max_daily_times"
              :min="0"
              controls-position="right"
              style="width: 100%"
            />
            <div class="text-xs text-gray-500 mt-1">0表示无限制</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="formData.priority" style="width: 100%">
              <el-option
                v-for="item in PRIORITY_OPTIONS"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 触发条件配置 -->
      <div class="condition-section">
        <div class="condition-title">触发条件配置</div>

        <!-- 冥想完成条件 -->
        <div v-if="formData.rule_type === 'meditation_complete'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="最少时长(秒)">
                <el-input-number
                  v-model="formData.condition.min_duration"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="冥想类型">
                <el-select
                  v-model="formData.condition.meditation_type"
                  placeholder="请选择冥想类型"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in MEDITATION_TYPE_OPTIONS"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 特殊事件条件 -->
        <div v-if="formData.rule_type === 'special_event'">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="事件ID">
                <el-input
                  v-model="formData.condition.event_id"
                  placeholder="请输入事件ID"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开始日期">
                <el-date-picker
                  v-model="formData.condition.start_date"
                  type="date"
                  placeholder="选择开始日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束日期">
                <el-date-picker
                  v-model="formData.condition.end_date"
                  type="date"
                  placeholder="选择结束日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 其他规则类型的提示 -->
        <div
          v-if="
            !['meditation_complete', 'special_event'].includes(
              formData.rule_type
            )
          "
        >
          <el-alert
            title="该规则类型暂无特殊条件配置"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <!-- 规则描述 -->
      <el-form-item label="规则描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入规则描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 状态 -->
      <el-form-item label="状态">
        <el-radio-group v-model="formData.is_active">
          <el-radio :value="true">启用</el-radio>
          <el-radio :value="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.condition-section {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;

  .condition-title {
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
  }
}
</style>
