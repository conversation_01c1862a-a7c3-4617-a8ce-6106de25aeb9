<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  updateUserLevel,
  updatePlantEnergy,
  type GrowthRecord,
  type UpdateUserLevelParams,
  type UpdatePlantEnergyParams
} from "@/api/growth-records";

defineOptions({
  name: "EditGrowthDialog"
});

interface Props {
  visible: boolean;
  data: GrowthRecord | null;
  type: "level" | "energy";
  plantData?: any;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const loading = ref(false);
const dialogVisible = ref(false);

// 表单数据
const levelForm = reactive({
  level: 1,
  reason: ""
});

const energyForm = reactive({
  energy_change: 0,
  reason: ""
});

// 表单验证规则
const levelRules = {
  level: [
    { required: true, message: "请输入等级", trigger: "blur" },
    { type: "number", min: 1, max: 100, message: "等级范围为1-100", trigger: "blur" }
  ],
  reason: [
    { required: true, message: "请输入调整原因", trigger: "blur" },
    { min: 2, max: 100, message: "原因长度为2-100个字符", trigger: "blur" }
  ]
};

const energyRules = {
  energy_change: [
    { required: true, message: "请输入能量变化值", trigger: "blur" },
    { type: "number", min: -9999, max: 9999, message: "能量变化值范围为-9999到9999", trigger: "blur" }
  ],
  reason: [
    { required: true, message: "请输入调整原因", trigger: "blur" },
    { min: 2, max: 100, message: "原因长度为2-100个字符", trigger: "blur" }
  ]
};

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val && props.data) {
      initForm();
    }
  },
  { immediate: true }
);

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit("update:visible", val);
});

// 初始化表单
const initForm = () => {
  if (props.type === "level" && props.data) {
    levelForm.level = props.data.meditation_level;
    levelForm.reason = "";
  } else if (props.type === "energy" && props.plantData) {
    energyForm.energy_change = 0;
    energyForm.reason = "";
  }
};

// 获取对话框标题
const getDialogTitle = () => {
  if (props.type === "level") {
    return "调整用户等级";
  } else if (props.type === "energy") {
    return "调整多肉能量";
  }
  return "编辑";
};

// 获取当前信息
const getCurrentInfo = () => {
  if (props.type === "level" && props.data) {
    return `当前等级: Lv.${props.data.meditation_level}`;
  } else if (props.type === "energy" && props.plantData) {
    return `当前能量: ${props.plantData.energy_value}`;
  }
  return "";
};

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true;
    
    if (props.type === "level" && props.data) {
      const params: UpdateUserLevelParams = {
        level: levelForm.level,
        reason: levelForm.reason
      };
      const response = await updateUserLevel(props.data.id, params);
      if (response.code === 200) {
        ElMessage.success("等级更新成功");
        emit("success");
        dialogVisible.value = false;
      } else {
        ElMessage.error(response.message || "等级更新失败");
      }
    } else if (props.type === "energy" && props.data && props.plantData) {
      const params: UpdatePlantEnergyParams = {
        energy_change: energyForm.energy_change,
        reason: energyForm.reason
      };
      const response = await updatePlantEnergy(props.data.id, props.plantData.id, params);
      if (response.code === 200) {
        ElMessage.success("能量更新成功");
        emit("success");
        dialogVisible.value = false;
      } else {
        ElMessage.error(response.message || "能量更新失败");
      }
    }
  } catch (error) {
    console.error("更新失败:", error);
    ElMessage.error("更新失败");
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <!-- 当前信息提示 -->
    <div class="mb-4 p-3 bg-blue-50 rounded-lg">
      <div class="text-sm text-blue-600">{{ getCurrentInfo() }}</div>
      <div v-if="data" class="text-xs text-gray-500 mt-1">
        用户: {{ data.nickname }} (ID: {{ data.id }})
      </div>
      <div v-if="type === 'energy' && plantData" class="text-xs text-gray-500 mt-1">
        多肉: {{ plantData.species }} (等级: {{ plantData.level }})
      </div>
    </div>

    <!-- 等级调整表单 -->
    <el-form
      v-if="type === 'level'"
      ref="levelFormRef"
      :model="levelForm"
      :rules="levelRules"
      label-width="100px"
    >
      <el-form-item label="新等级" prop="level">
        <el-input-number
          v-model="levelForm.level"
          :min="1"
          :max="100"
          :step="1"
          placeholder="请输入新等级"
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="调整原因" prop="reason">
        <el-input
          v-model="levelForm.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入调整原因，如：管理员手动调整等级"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <!-- 能量调整表单 -->
    <el-form
      v-if="type === 'energy'"
      ref="energyFormRef"
      :model="energyForm"
      :rules="energyRules"
      label-width="100px"
    >
      <el-form-item label="能量变化" prop="energy_change">
        <el-input-number
          v-model="energyForm.energy_change"
          :min="-9999"
          :max="9999"
          :step="1"
          placeholder="请输入能量变化值"
          class="w-full"
        />
        <div class="text-xs text-gray-500 mt-1">
          正数表示增加能量，负数表示减少能量
        </div>
      </el-form-item>
      <el-form-item label="调整原因" prop="reason">
        <el-input
          v-model="energyForm.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入调整原因，如：管理员奖励能量、管理员扣除能量等"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
}
</style>
