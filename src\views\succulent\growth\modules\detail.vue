<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getGrowthRecordDetail,
  type GrowthRecord
} from "@/api/growth-records";

defineOptions({
  name: "GrowthRecordDetail"
});

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const detailData = ref<GrowthRecord | null>(null);

// 获取用户详情
const fetchDetail = async () => {
  try {
    loading.value = true;
    const userId = route.params.id as string;
    const response = await getGrowthRecordDetail(userId);
    if (response.code === 200) {
      detailData.value = response.data;
    } else {
      ElMessage.error(response.message || "获取详情失败");
    }
  } catch (error) {
    console.error("获取详情失败:", error);
    ElMessage.error("获取详情失败");
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleString("zh-CN");
};

// 获取健康状态文本和颜色
const getHealthStatusInfo = (status: string) => {
  const statusMap = {
    healthy: { text: "健康", color: "success" },
    excellent: { text: "优秀", color: "primary" },
    unhealthy: { text: "不健康", color: "danger" }
  };
  return statusMap[status] || { text: "未知", color: "info" };
};

// 返回列表
const goBack = () => {
  router.push("/succulent/growth");
};

onMounted(() => {
  fetchDetail();
});
</script>

<template>
  <div v-loading="loading" class="main">
    <!-- 头部 -->
    <div class="mb-6 flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-gray-800 mb-2">成长记录详情</h2>
        <p class="text-gray-600">查看用户的详细成长信息</p>
      </div>
      <el-button
        type="primary"
        :icon="useRenderIcon('ep:back')"
        @click="goBack"
      >
        返回列表
      </el-button>
    </div>

    <div v-if="detailData" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 用户基本信息 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">用户信息</h3>
        <div class="flex items-center mb-4">
          <el-avatar :size="60" :src="detailData.avatar_url" class="mr-4" />
          <div>
            <div class="font-medium text-lg">{{ detailData.nickname }}</div>
            <div class="text-sm text-gray-500">ID: {{ detailData.id }}</div>
          </div>
        </div>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-600">冥想等级:</span>
            <span class="font-medium"
              >Lv.{{ detailData.meditation_level }}</span
            >
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">连续天数:</span>
            <span class="font-medium">{{ detailData.streak_days }}天</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">成长天数:</span>
            <span class="font-medium">{{ detailData.growth_days }}天</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">健康状态:</span>
            <el-tag :type="getHealthStatusInfo(detailData.health_status).color">
              {{ getHealthStatusInfo(detailData.health_status).text }}
            </el-tag>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">注册时间:</span>
            <span class="text-sm">{{ formatDate(detailData.created_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 多肉植物信息 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">多肉植物</h3>
        <div v-if="detailData.plants.length > 0" class="space-y-4">
          <div
            v-for="plant in detailData.plants"
            :key="plant.id"
            class="border rounded-lg p-4"
          >
            <div class="flex items-center mb-3">
              <div
                class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3"
              >
                <span class="text-green-600 text-xl">🌱</span>
              </div>
              <div>
                <div class="font-medium">{{ plant.species }}</div>
                <div class="text-sm text-gray-500">等级: {{ plant.level }}</div>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">能量值:</span>
                <span class="font-medium text-orange-600">{{
                  plant.energy_value
                }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">创建时间:</span>
                <span>{{ formatDate(plant.created_at).split(" ")[0] }}</span>
              </div>
            </div>

            <!-- 成长记录 -->
            <div v-if="plant.growth_records.length > 0" class="mt-3">
              <div class="text-sm font-medium text-gray-700 mb-2">
                最近成长记录:
              </div>
              <div class="max-h-32 overflow-y-auto space-y-1">
                <div
                  v-for="record in plant.growth_records.slice(0, 5)"
                  :key="record.id"
                  class="text-xs bg-gray-50 rounded p-2"
                >
                  <div class="flex justify-between items-center">
                    <span
                      :class="
                        record.change_value > 0
                          ? 'text-green-600'
                          : 'text-red-600'
                      "
                    >
                      {{ record.change_value > 0 ? "+" : "" }}{{
                        record.change_value
                      }}
                    </span>
                    <span class="text-gray-500">{{
                      formatDate(record.created_at).split(" ")[0]
                    }}</span>
                  </div>
                  <div class="text-gray-600 mt-1">{{ record.reason }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center text-gray-400 py-8">暂无多肉植物</div>
      </div>

      <!-- 统计信息 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">统计信息</h3>
        <div class="space-y-4">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">
              {{ detailData.total_energy }}
            </div>
            <div class="text-sm text-gray-600">总能量值</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">
              {{ detailData.plant_count }}
            </div>
            <div class="text-sm text-gray-600">多肉数量</div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">
              {{ detailData.avg_plant_level.toFixed(1) }}
            </div>
            <div class="text-sm text-gray-600">平均多肉等级</div>
          </div>
          <div class="text-center p-4 bg-orange-50 rounded-lg">
            <div class="text-2xl font-bold text-orange-600">
              {{ detailData.achievements.length }}
            </div>
            <div class="text-sm text-gray-600">获得成就</div>
          </div>
        </div>
      </div>

      <!-- 成就列表 -->
      <div
        v-if="detailData.achievements.length > 0"
        class="lg:col-span-3 bg-white rounded-lg shadow-md p-6"
      >
        <h3 class="text-lg font-semibold text-gray-800 mb-4">获得成就</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="achievement in detailData.achievements"
            :key="achievement.id"
            class="border rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-center mb-2">
              <div
                class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3"
              >
                <span class="text-yellow-600">🏆</span>
              </div>
              <div class="font-medium">{{ achievement.achievement_name }}</div>
            </div>
            <div class="text-sm text-gray-600 mb-2">
              {{ achievement.description }}
            </div>
            <div class="text-xs text-gray-500">
              获得时间: {{ formatDate(achievement.unlocked_at) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!detailData && !loading" class="text-center py-12">
      <div class="text-gray-400 text-lg mb-4">未找到用户信息</div>
      <el-button type="primary" @click="goBack">返回列表</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
}
</style>
