const Layout = () => import("@/layout/index.vue");

export default {
  path: "/succulent",
  name: "Succulent",
  component: Layout,
  redirect: "/succulent/variety",
  meta: {
    icon: "ep/orange",
    title: "多肉养成系统",
    rank: 3
  },
  children: [
    {
      path: "/succulent/variety",
      name: "SucculentVariety",
      component: () => import("@/views/succulent/variety/index.vue"),
      meta: {
        title: "多肉品种管理",
        showLink: true
      }
    },
    {
      path: "/succulent/level",
      name: "SucculentLevel",
      component: () => import("@/views/succulent/level/index.vue"),
      meta: {
        title: "等级系统",
        showLink: true
      }
    },
    {
      path: "/succulent/reward",
      name: "SucculentReward",
      component: () => import("@/views/succulent/reward/index.vue"),
      meta: {
        title: "奖励机制",
        showLink: true
      }
    },
    {
      path: "/succulent/growth",
      name: "SucculentGrowth",
      component: () => import("@/views/succulent/growth/index.vue"),
      meta: {
        title: "成长记录",
        showLink: true
      }
    },
    {
      path: "/succulent/growth/statistics",
      name: "GrowthStatistics",
      component: () => import("@/views/succulent/growth/statistics.vue"),
      meta: {
        title: "成长统计",
        showLink: true
      }
    },
    {
      path: "/succulent/growth/:id/detail",
      name: "GrowthRecordDetail",
      component: () => import("@/views/succulent/growth/modules/detail.vue"),
      meta: {
        title: "成长记录详情",
        showLink: false
      }
    }
  ]
} satisfies RouteConfigsTable;
