import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 规则条件类型定义
export type RuleCondition = {
  min_duration?: number;
  meditation_type?: string;
  event_id?: string;
  start_date?: string;
  end_date?: string;
  [key: string]: any;
};

// 能量奖励规则数据类型定义
export type EnergyRule = {
  id: string;
  rule_name: string;
  rule_type: string;
  condition: RuleCondition;
  energy_amount: number;
  bonus_multiplier: number;
  max_daily_times: number;
  description: string;
  priority: number;
  is_active: boolean;
  created_at: string;
  updated_at?: string | null;
};

// 分页信息类型定义
export type Pagination = {
  page: number;
  limit: number;
  total: number;
  pages: number;
};

// 能量奖励规则列表响应类型
export type EnergyRuleListResponse = {
  code: number;
  message: string;
  data: {
    list: EnergyRule[];
    pagination: Pagination;
  };
};

// 能量奖励规则详情响应类型
export type EnergyRuleResponse = {
  code: number;
  message: string;
  data: EnergyRule;
};

// 通用响应类型
export type CommonResponse = {
  code: number;
  message: string;
};

// 能量奖励规则列表查询参数
export type EnergyRuleListParams = {
  page?: number;
  limit?: number;
  rule_type?: string;
  is_active?: boolean;
};

// 能量奖励规则创建参数
export type CreateEnergyRuleParams = {
  rule_name: string;
  rule_type: string;
  condition: RuleCondition;
  energy_amount: number;
  bonus_multiplier: number;
  max_daily_times: number;
  description: string;
  priority: number;
  is_active?: boolean;
};

// 能量奖励规则更新参数
export type UpdateEnergyRuleParams = {
  rule_name?: string;
  condition?: RuleCondition;
  energy_amount?: number;
  bonus_multiplier?: number;
  max_daily_times?: number;
  description?: string;
  priority?: number;
};

/** 获取能量奖励规则列表 */
export const getEnergyRuleList = (params?: EnergyRuleListParams) => {
  return http.request<EnergyRuleListResponse>(
    "get", 
    baseUrlApi("admin/plants/energy-rules"), 
    { params }
  );
};

/** 获取指定规则详情 */
export const getEnergyRuleDetail = (id: string) => {
  return http.request<EnergyRuleResponse>(
    "get", 
    baseUrlApi(`admin/plants/energy-rules/${id}`)
  );
};

/** 创建新的能量奖励规则 */
export const createEnergyRule = (data: CreateEnergyRuleParams) => {
  return http.request<EnergyRuleResponse>(
    "post", 
    baseUrlApi("admin/plants/energy-rules"), 
    { data }
  );
};

/** 更新能量奖励规则 */
export const updateEnergyRule = (id: string, data: UpdateEnergyRuleParams) => {
  return http.request<EnergyRuleResponse>(
    "put", 
    baseUrlApi(`admin/plants/energy-rules/${id}`), 
    { data }
  );
};

/** 切换规则状态（启用/禁用） */
export const toggleEnergyRuleStatus = (id: string) => {
  return http.request<CommonResponse>(
    "put", 
    baseUrlApi(`admin/plants/energy-rules/${id}/toggle`)
  );
};

/** 删除能量奖励规则 */
export const deleteEnergyRule = (id: string) => {
  return http.request<CommonResponse>(
    "delete", 
    baseUrlApi(`admin/plants/energy-rules/${id}`)
  );
};

/** 获取所有启用的规则（用于前端下拉选择） */
export const getActiveEnergyRules = () => {
  return http.request<EnergyRuleListResponse>(
    "get", 
    baseUrlApi("admin/plants/energy-rules"), 
    { 
      params: { 
        is_active: true, 
        limit: 100 
      } 
    }
  );
};

/** 获取所有禁用的规则 */
export const getInactiveEnergyRules = () => {
  return http.request<EnergyRuleListResponse>(
    "get", 
    baseUrlApi("admin/plants/energy-rules"), 
    { 
      params: { 
        is_active: false 
      } 
    }
  );
};

// 规则类型常量
export const RULE_TYPES = {
  MEDITATION_COMPLETE: 'meditation_complete',
  DAILY_LOGIN: 'daily_login',
  CONTINUOUS_DAYS: 'continuous_days',
  SPECIAL_EVENT: 'special_event',
  ACHIEVEMENT: 'achievement',
  LEVEL_UP: 'level_up'
} as const;

// 规则类型选项
export const RULE_TYPE_OPTIONS = [
  { label: '冥想完成', value: RULE_TYPES.MEDITATION_COMPLETE },
  { label: '每日登录', value: RULE_TYPES.DAILY_LOGIN },
  { label: '连续天数', value: RULE_TYPES.CONTINUOUS_DAYS },
  { label: '特殊事件', value: RULE_TYPES.SPECIAL_EVENT },
  { label: '成就解锁', value: RULE_TYPES.ACHIEVEMENT },
  { label: '等级提升', value: RULE_TYPES.LEVEL_UP }
];

// 冥想类型选项
export const MEDITATION_TYPE_OPTIONS = [
  { label: '基础冥想', value: 'basic' },
  { label: '深度冥想', value: 'deep' },
  { label: '正念冥想', value: 'mindfulness' },
  { label: '呼吸冥想', value: 'breathing' },
  { label: '身体扫描', value: 'body_scan' }
];

// 优先级选项
export const PRIORITY_OPTIONS = [
  { label: '最高', value: 20 },
  { label: '高', value: 15 },
  { label: '中', value: 10 },
  { label: '低', value: 5 },
  { label: '最低', value: 1 }
];
