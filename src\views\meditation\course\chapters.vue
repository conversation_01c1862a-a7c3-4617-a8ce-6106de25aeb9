<template>
  <div class="main">
    <!-- 课程信息 -->
    <el-card class="mb-4" v-if="courseInfo">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-semibold">课程信息</span>
          <el-button @click="$router.go(-1)" :icon="useRenderIcon('ep:back')">
            返回课程列表
          </el-button>
        </div>
      </template>
      <div>
        <h3 class="text-xl mb-2">{{ courseInfo.title }}</h3>
        <p class="text-gray-600">{{ courseInfo.description }}</p>
      </div>
    </el-card>

    <!-- 章节管理工具栏 -->
    <PureTableBar title="章节管理" :columns="columns" @refresh="loadChapters">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAddChapter"
        >
          添加章节
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:sort')"
          @click="handleReorderChapters"
          :disabled="!chapters.length"
        >
          调整顺序
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 章节列表 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="chapters"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #cover="{ row }">
            <el-image
              v-if="row.cover_url"
              :src="row.cover_url"
              style="width: 60px; height: 60px"
              fit="cover"
              :preview-src-list="[row.cover_url]"
            />
            <span v-else class="text-gray-400">无封面</span>
          </template>

          <template #duration="{ row }">
            <span>{{ formatDuration(row.duration) }}</span>
          </template>

          <template #tags="{ row }">
            <div class="flex flex-wrap gap-1">
              <el-tag v-for="tag in row.tags" :key="tag.id" size="small">
                {{ tag.name }}
              </el-tag>
            </div>
          </template>

          <template #is_recommended="{ row }">
            <el-tag
              :type="row.is_recommended ? 'success' : 'info'"
              size="small"
            >
              {{ row.is_recommended ? "推荐" : "普通" }}
            </el-tag>
          </template>

          <template #status="{ row }">
            <el-tag :type="getStatusTag(row.status).type" size="small">
              {{ getStatusTag(row.status).text }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEditChapter(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDeleteChapter(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 添加/编辑章节对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingChapter ? '编辑章节' : '添加章节'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="chapterForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="章节标题" prop="title">
          <el-input v-model="chapterForm.title" placeholder="请输入章节标题" />
        </el-form-item>
        <el-form-item label="章节描述" prop="description">
          <el-input
            v-model="chapterForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入章节描述"
          />
        </el-form-item>
        <el-form-item label="封面图片">
          <el-input
            v-model="chapterForm.cover_url"
            placeholder="请输入封面图片URL"
          />
        </el-form-item>
        <el-form-item label="音频文件">
          <el-input
            v-model="chapterForm.audio_url"
            placeholder="请输入音频文件URL"
          />
        </el-form-item>
        <el-form-item label="视频文件">
          <el-input
            v-model="chapterForm.video_url"
            placeholder="请输入视频文件URL"
          />
        </el-form-item>
        <el-form-item label="时长(秒)" prop="duration">
          <el-input-number
            v-model="chapterForm.duration"
            :min="0"
            :step="60"
            placeholder="请输入时长"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="chapterForm.tag_ids"
            multiple
            placeholder="请选择标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in allTags"
              :key="tag.id"
              :label="tag.name"
              :value="parseInt(tag.id)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number
            v-model="chapterForm.sort_order"
            :min="1"
            placeholder="请输入排序"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveChapter" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 调整顺序对话框 -->
    <el-dialog
      v-model="reorderDialogVisible"
      title="调整章节顺序"
      width="500px"
    >
      <div class="space-y-2">
        <div
          v-for="(chapter, index) in reorderList"
          :key="chapter.id"
          class="flex items-center justify-between p-3 border rounded"
        >
          <span>{{ chapter.title }}</span>
          <div class="flex items-center space-x-2">
            <el-input-number
              v-model="chapter.sort_order"
              :min="1"
              :max="reorderList.length"
              size="small"
            />
            <el-button
              size="small"
              :icon="useRenderIcon('ep:top')"
              @click="moveUp(index)"
              :disabled="index === 0"
            />
            <el-button
              size="small"
              :icon="useRenderIcon('ep:bottom')"
              @click="moveDown(index)"
              :disabled="index === reorderList.length - 1"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="reorderDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveReorder" :loading="saving">
          保存顺序
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, ElMessageBox, type FormInstance } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getCourseChapters,
  createCourseChapter,
  deleteCourseChapter,
  reorderCourseChapters,
  getMeditationTagList,
  type CourseChapter,
  type CreateChapterParams,
  type ReorderChaptersParams,
  type MeditationTag
} from "@/api/meditation";

defineOptions({
  name: "CourseChapters"
});

const route = useRoute();
const courseId = route.params.id as string;

const loading = ref(false);
const saving = ref(false);
const dialogVisible = ref(false);
const reorderDialogVisible = ref(false);
const formRef = ref<FormInstance>();

const courseInfo = ref<{
  id: string;
  title: string;
  description: string;
} | null>(null);

const chapters = ref<CourseChapter[]>([]);
const allTags = ref<MeditationTag[]>([]);
const editingChapter = ref<CourseChapter | null>(null);
const reorderList = ref<CourseChapter[]>([]);

const chapterForm = reactive<CreateChapterParams>({
  title: "",
  description: "",
  cover_url: "",
  audio_url: "",
  video_url: "",
  duration: 0,
  tag_ids: [],
  sort_order: 1
});

const formRules = {
  title: [{ required: true, message: "请输入章节标题", trigger: "blur" }],
  description: [{ required: true, message: "请输入章节描述", trigger: "blur" }],
  duration: [{ required: true, message: "请输入时长", trigger: "blur" }]
};

const columns = [
  {
    label: "排序",
    prop: "sort_order",
    width: 80
  },
  {
    label: "封面",
    slot: "cover",
    width: 80
  },
  {
    label: "标题",
    prop: "title",
    minWidth: 200
  },
  {
    label: "描述",
    prop: "description",
    minWidth: 200
  },
  {
    label: "时长",
    slot: "duration",
    width: 100
  },
  {
    label: "标签",
    slot: "tags",
    minWidth: 150
  },
  {
    label: "收藏数",
    prop: "favorite_count",
    width: 100
  },
  {
    label: "推荐",
    slot: "is_recommended",
    width: 80
  },
  {
    label: "状态",
    slot: "status",
    width: 80
  },
  {
    label: "操作",
    slot: "operation",
    fixed: "right",
    width: 150
  }
];

// 加载章节列表
const loadChapters = async () => {
  loading.value = true;
  try {
    const { data } = await getCourseChapters(courseId);
    courseInfo.value = data.course;
    chapters.value = data.chapters.sort((a, b) => a.sort_order - b.sort_order);
  } catch (error) {
    ElMessage.error("获取章节列表失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 加载标签列表
const loadTags = async () => {
  try {
    const { data } = await getMeditationTagList();
    allTags.value = data.items;
  } catch (error) {
    console.error("获取标签列表失败:", error);
  }
};

// 添加章节
const handleAddChapter = () => {
  editingChapter.value = null;
  chapterForm.sort_order = chapters.value.length + 1;
  dialogVisible.value = true;
};

// 编辑章节
const handleEditChapter = (chapter: CourseChapter) => {
  editingChapter.value = chapter;
  Object.assign(chapterForm, {
    title: chapter.title,
    description: chapter.description,
    cover_url: chapter.cover_url || "",
    audio_url: chapter.audio_url || "",
    video_url: chapter.video_url || "",
    duration: chapter.duration,
    tag_ids: chapter.tags.map(tag => parseInt(tag.id)),
    sort_order: chapter.sort_order
  });
  dialogVisible.value = true;
};

// 保存章节
const handleSaveChapter = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    if (editingChapter.value) {
      // TODO: 实现编辑章节接口
      ElMessage.info("编辑章节功能待实现");
    } else {
      await createCourseChapter(courseId, chapterForm);
      ElMessage.success("添加章节成功");
    }

    dialogVisible.value = false;
    loadChapters();
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      ElMessage.error("保存章节失败");
      console.error(error);
    }
  } finally {
    saving.value = false;
  }
};

// 删除章节
const handleDeleteChapter = async (chapter: CourseChapter) => {
  try {
    await ElMessageBox.confirm(`确定要删除章节 ${chapter.title} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    await deleteCourseChapter(courseId, chapter.id);
    ElMessage.success("删除章节成功");
    loadChapters();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除章节失败");
      console.error(error);
    }
  }
};

// 调整章节顺序
const handleReorderChapters = () => {
  reorderList.value = [...chapters.value];
  reorderDialogVisible.value = true;
};

// 上移
const moveUp = (index: number) => {
  if (index > 0) {
    const temp = reorderList.value[index];
    reorderList.value[index] = reorderList.value[index - 1];
    reorderList.value[index - 1] = temp;

    // 更新排序号
    reorderList.value.forEach((item, idx) => {
      item.sort_order = idx + 1;
    });
  }
};

// 下移
const moveDown = (index: number) => {
  if (index < reorderList.value.length - 1) {
    const temp = reorderList.value[index];
    reorderList.value[index] = reorderList.value[index + 1];
    reorderList.value[index + 1] = temp;

    // 更新排序号
    reorderList.value.forEach((item, idx) => {
      item.sort_order = idx + 1;
    });
  }
};

// 保存顺序
const handleSaveReorder = async () => {
  try {
    saving.value = true;
    const reorderData: ReorderChaptersParams = {
      chapter_orders: reorderList.value.map(chapter => ({
        id: parseInt(chapter.id),
        sort_order: chapter.sort_order
      }))
    };

    await reorderCourseChapters(courseId, reorderData);
    ElMessage.success("调整顺序成功");
    reorderDialogVisible.value = false;
    loadChapters();
  } catch (error) {
    ElMessage.error("调整顺序失败");
    console.error(error);
  } finally {
    saving.value = false;
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(chapterForm, {
    title: "",
    description: "",
    cover_url: "",
    audio_url: "",
    video_url: "",
    duration: 0,
    tag_ids: [],
    sort_order: 1
  });
};

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return hours > 0 ? `${hours}h${mins}m` : `${mins}m`;
};

// 获取状态标签
const getStatusTag = (status: string) => {
  const statusMap = {
    draft: { type: "info", text: "草稿" },
    published: { type: "success", text: "已发布" },
    archived: { type: "warning", text: "已归档" }
  };
  return statusMap[status] || { type: "info", text: "未知" };
};

onMounted(() => {
  loadTags();
  loadChapters();
});
</script>
