import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 管理员数据类型定义
export type Admin = {
  id: string;
  username: string;
  real_name: string;
  email?: string;
  phone?: string;
  role: "super_admin" | "admin" | "editor";
  status: "active" | "inactive";
  last_login_at?: string;
  created_at: string;
  updated_at?: string;
};

// 管理员列表响应类型
export type AdminListResponse = {
  code: number;
  message?: string;
  data: {
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    items: Admin[];
  };
};

// 管理员创建/更新响应类型
export type AdminResponse = {
  code: number;
  message?: string;
  data: Admin;
};

// 管理员列表查询参数
export type AdminListParams = {
  page?: number;
  limit?: number;
  search?: string;
};

// 管理员创建参数
export type CreateAdminParams = {
  username: string;
  password: string;
  real_name: string;
  email?: string;
  phone?: string;
  role: "super_admin" | "admin" | "editor";
};

// 管理员更新参数
export type UpdateAdminParams = {
  real_name?: string;
  email?: string;
  phone?: string;
  role?: "super_admin" | "admin" | "editor";
  status?: "active" | "inactive";
};

/** 获取管理员列表 */
export const getAdminList = (params?: AdminListParams) => {
  return http.request<AdminListResponse>("get", baseUrlApi("admin/admins"), {
    params
  });
};

/** 创建管理员 */
export const createAdmin = (data: CreateAdminParams) => {
  return http.request<AdminResponse>("post", baseUrlApi("admin/admins"), {
    data
  });
};

/** 更新管理员信息 */
export const updateAdmin = (id: string, data: UpdateAdminParams) => {
  return http.request<AdminResponse>("put", baseUrlApi(`admin/admins/${id}`), {
    data
  });
};

/** 删除管理员 */
export const deleteAdmin = (id: string) => {
  return http.request<{ code: number; message?: string }>(
    "delete",
    baseUrlApi(`admin/admins/${id}`)
  );
};
