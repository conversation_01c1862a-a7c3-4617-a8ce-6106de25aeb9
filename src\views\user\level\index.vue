<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules
} from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getUserLevelList,
  createUserLevel,
  updateUserLevel,
  deleteUserLevel,
  type UserLevel,
  type CreateUserLevelParams,
  type UpdateUserLevelParams
} from "@/api/userLevels";

defineOptions({
  name: "UserLevel"
});

const loading = ref(false);
const tableData = ref<UserLevel[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);
const formRef = ref<FormInstance>();

const formData = reactive({
  id: "",
  level: 1,
  level_name: "",
  icon_url: "",
  required_days: 0,
  required_duration: 0,
  benefits: {
    description: "",
    features: [] as string[]
  }
});

const rules: FormRules = {
  level: [{ required: true, message: "请输入等级数值", trigger: "blur" }],
  level_name: [
    { required: true, message: "请输入等级名称", trigger: "blur" },
    {
      min: 2,
      max: 20,
      message: "等级名称长度在 2 到 20 个字符",
      trigger: "blur"
    }
  ],
  required_days: [
    { required: true, message: "请输入所需天数", trigger: "blur" }
  ],
  required_duration: [
    { required: true, message: "请输入所需时长", trigger: "blur" }
  ],
  "benefits.description": [
    { required: true, message: "请输入等级描述", trigger: "blur" }
  ]
};

// 权益选项
const featureOptions = [
  "基础冥想内容",
  "进阶冥想内容",
  "专属课程",
  "多肉种植",
  "多肉进阶品种",
  "稀有多肉品种",
  "限量多肉",
  "社区管理",
  "专属定制",
  "解锁传说多肉",
  "能量获取+50%",
  "能量获取+100%",
  "能量获取+150%",
  "专属头像框",
  "专属称号"
];

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString("zh-CN");
};

const columns = [
  {
    label: "等级",
    prop: "level",
    minWidth: 80
  },
  {
    label: "等级名称",
    prop: "level_name",
    minWidth: 120
  },
  {
    label: "图标",
    slot: "icon",
    minWidth: 80
  },
  {
    label: "所需天数",
    prop: "required_days",
    minWidth: 100
  },
  {
    label: "所需时长(秒)",
    prop: "required_duration",
    minWidth: 140
  },
  {
    label: "等级权益",
    slot: "benefits",
    minWidth: 200
  },
  {
    label: "创建时间",
    slot: "created_at",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 180,
    slot: "operation"
  }
];

const loadData = async () => {
  loading.value = true;
  try {
    const response = await getUserLevelList();
    if (response.code === 200) {
      tableData.value = response.data;
    } else {
      ElMessage.error(response.message || "获取等级配置失败");
    }
  } catch (error) {
    console.error("获取等级配置失败:", error);
    ElMessage.error("获取等级配置失败");
  } finally {
    loading.value = false;
  }
};

const handleAdd = () => {
  dialogTitle.value = "新增等级";
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

const handleEdit = (row: UserLevel) => {
  dialogTitle.value = "编辑等级";
  isEdit.value = true;
  formData.id = row.id;
  formData.level = row.level;
  formData.level_name = row.level_name;
  formData.icon_url = row.icon_url || "";
  formData.required_days = row.required_days;
  formData.required_duration = row.required_duration;
  formData.benefits = { ...row.benefits };
  dialogVisible.value = true;
};

const handleDelete = async (row: UserLevel) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除等级 ${row.level_name} 吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deleteUserLevel(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      loadData();
    } else {
      ElMessage.error(response.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除等级配置失败:", error);
      ElMessage.error("删除等级配置失败");
    }
  }
};

const resetForm = () => {
  formData.id = "";
  formData.level = 1;
  formData.level_name = "";
  formData.icon_url = "";
  formData.required_days = 0;
  formData.required_duration = 0;
  formData.benefits = {
    description: "",
    features: []
  };
  formRef.value?.resetFields();
};

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    if (isEdit.value) {
      const updateData: UpdateUserLevelParams = {
        level_name: formData.level_name,
        required_days: formData.required_days,
        required_duration: formData.required_duration,
        benefits: formData.benefits
      };
      if (formData.icon_url) {
        updateData.icon_url = formData.icon_url;
      }

      const response = await updateUserLevel(formData.id, updateData);
      if (response.code === 200) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.message || "编辑失败");
      }
    } else {
      const createData: CreateUserLevelParams = {
        level: formData.level,
        level_name: formData.level_name,
        required_days: formData.required_days,
        required_duration: formData.required_duration,
        benefits: formData.benefits
      };
      if (formData.icon_url) {
        createData.icon_url = formData.icon_url;
      }

      const response = await createUserLevel(createData);
      if (response.code === 200) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.message || "新增失败");
      }
    }
  } catch (error) {
    console.error("提交表单失败:", error);
    ElMessage.error("操作失败");
  }
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="main">
    <!-- 表格工具栏 -->
    <PureTableBar title="用户等级管理" :columns="columns" @refresh="loadData">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增等级
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #icon="{ row }">
            <img
              v-if="row.icon_url"
              :src="row.icon_url"
              alt="等级图标"
              style="width: 32px; height: 32px"
            />
            <span v-else>-</span>
          </template>

          <template #benefits="{ row }">
            <div>
              <div class="mb-1 text-sm text-gray-600">
                {{ row.benefits.description }}
              </div>
              <el-tag
                v-for="feature in row.benefits.features"
                :key="feature"
                size="small"
                class="mr-1 mb-1"
              >
                {{ feature }}
              </el-tag>
            </div>
          </template>

          <template #created_at="{ row }">
            {{ formatDate(row.created_at) }}
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      draggable
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        label-position="right"
      >
        <el-form-item label="等级数值" prop="level">
          <el-input-number
            v-model="formData.level"
            :min="1"
            :max="10"
            :disabled="isEdit"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="等级名称" prop="level_name">
          <el-input
            v-model="formData.level_name"
            placeholder="请输入等级名称"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="等级图标" prop="icon_url">
          <el-input
            v-model="formData.icon_url"
            placeholder="请输入等级图标URL"
            maxlength="200"
          />
        </el-form-item>
        <el-form-item label="所需天数" prop="required_days">
          <el-input-number
            v-model="formData.required_days"
            :min="0"
            controls-position="right"
          />
          <span class="ml-2 text-gray-500">天</span>
        </el-form-item>
        <el-form-item label="所需时长" prop="required_duration">
          <el-input-number
            v-model="formData.required_duration"
            :min="0"
            controls-position="right"
          />
          <span class="ml-2 text-gray-500">秒</span>
        </el-form-item>
        <el-form-item label="等级权益" prop="benefits.features">
          <el-select
            v-model="formData.benefits.features"
            multiple
            placeholder="请选择等级权益"
            style="width: 100%"
          >
            <el-option
              v-for="item in featureOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="等级描述" prop="benefits.description">
          <el-input
            v-model="formData.benefits.description"
            type="textarea"
            :rows="3"
            placeholder="请输入等级描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
