<script setup lang="ts">
import { computed } from "vue";
import type { Admin } from "@/api/admin";

interface Props {
  modelValue: boolean;
  data?: Admin;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
});

// 角色映射
const roleMap = {
  super_admin: "超级管理员",
  admin: "管理员",
  editor: "编辑员"
};

// 状态映射
const statusMap = {
  active: "正常",
  inactive: "禁用"
};

// 格式化时间
const formatTime = (time?: string) => {
  return time ? new Date(time).toLocaleString() : "-";
};
</script>

<template>
  <el-dialog
    v-model="visible"
    title="管理员详情"
    width="600px"
    draggable
    destroy-on-close
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">
          {{ data.id }}
        </el-descriptions-item>
        <el-descriptions-item label="用户名">
          {{ data.username }}
        </el-descriptions-item>
        <el-descriptions-item label="真实姓名">
          {{ data.real_name }}
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          {{ data.email || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ data.phone || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="角色">
          <el-tag
            :type="
              data.role === 'super_admin'
                ? 'danger'
                : data.role === 'admin'
                  ? 'warning'
                  : 'info'
            "
            size="small"
          >
            {{ roleMap[data.role] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag
            :type="data.status === 'active' ? 'success' : 'danger'"
            size="small"
          >
            {{ statusMap[data.status] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最后登录时间">
          {{ formatTime(data.last_login_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(data.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatTime(data.updated_at) }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.detail-content {
  padding: 20px 0;
}
</style>
