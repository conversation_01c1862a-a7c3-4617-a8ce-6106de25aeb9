const Layout = () => import("@/layout/index.vue");

export default {
  path: "/user",
  name: "User",
  component: Layout,
  redirect: "/user/list",
  meta: {
    icon: "ep/user",
    title: "用户管理",
    rank: 1
  },
  children: [
    {
      path: "/user/list",
      name: "UserList",
      component: () => import("@/views/user/list/index.vue"),
      meta: {
        title: "用户列表",
        showLink: true
      }
    },
    {
      path: "/user/level",
      name: "UserLevel",
      component: () => import("@/views/user/level/index.vue"),
      meta: {
        title: "用户等级管理",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
