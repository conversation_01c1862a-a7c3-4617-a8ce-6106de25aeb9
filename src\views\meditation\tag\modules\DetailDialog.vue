<template>
  <el-dialog
    v-model="visible"
    title="标签详情"
    width="600px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="标签名称">
          {{ data.name }}
        </el-descriptions-item>
        <el-descriptions-item label="标签颜色">
          <div class="color-display">
            <div 
              class="color-block" 
              :style="{ backgroundColor: data.color }"
            ></div>
            <span class="color-text">{{ data.color }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="使用次数">
          {{ data.useCount }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.status === 1 ? 'success' : 'danger'">
            {{ data.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">
          {{ data.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ data.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface Tag {
  id: number;
  name: string;
  color: string;
  description: string;
  useCount: number;
  status: number;
  createTime: string;
}

interface Props {
  modelValue: boolean;
  data?: Tag | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .color-block {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
  }
  
  .color-text {
    font-family: monospace;
    font-size: 12px;
    color: #606266;
  }
}
</style>
