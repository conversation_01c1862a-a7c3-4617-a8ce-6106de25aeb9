<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import * as echarts from "echarts";
import {
  getGrowthRecordStatistics,
  type GrowthRecordStatisticsResponse
} from "@/api/growth-records";

defineOptions({
  name: "GrowthStatistics"
});

const loading = ref(false);
const statisticsData = ref<any>({
  user_stats: {
    total_users: 0,
    avg_level: 0,
    avg_streak_days: 0,
    max_streak_days: 0
  },
  plant_stats: {
    total_plants: 0,
    avg_energy: 0,
    avg_level: 0,
    max_level: 0
  },
  growth_record_stats: {
    total_records: 0,
    total_energy_change: 0
  },
  level_distribution: [],
  health_status_stats: {}
});

// 图表实例
const levelChartRef = ref<HTMLDivElement>();
const healthChartRef = ref<HTMLDivElement>();
let levelChart: echarts.ECharts | null = null;
let healthChart: echarts.ECharts | null = null;

// 获取统计数据
const fetchStatistics = async () => {
  try {
    loading.value = true;
    const response = await getGrowthRecordStatistics();
    if (response.code === 200) {
      statisticsData.value = response.data;
      // 数据更新后重新渲染图表
      await nextTick();
      updateCharts();
    } else {
      ElMessage.error(response.message || "获取统计数据失败");
    }
  } catch (error) {
    console.error("获取统计数据失败:", error);
    ElMessage.error("获取统计数据失败");
  } finally {
    loading.value = false;
  }
};

// 初始化图表
const initCharts = () => {
  if (levelChartRef.value) {
    levelChart = echarts.init(levelChartRef.value);
  }
  if (healthChartRef.value) {
    healthChart = echarts.init(healthChartRef.value);
  }
  updateCharts();
};

// 更新图表数据
const updateCharts = () => {
  updateLevelChart();
  updateHealthChart();
};

// 更新冥想等级分布图表
const updateLevelChart = () => {
  if (!levelChart || !statisticsData.value.level_distribution) return;

  const data = statisticsData.value.level_distribution.map((item: any) => ({
    name: `等级 ${item.meditation_level}`,
    value: parseInt(item.user_count)
  }));

  const option = {
    title: {
      text: "冥想等级分布",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold"
      }
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      left: "left",
      top: "middle"
    },
    series: [
      {
        name: "冥想等级分布",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["60%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: data,
        color: [
          "#5470c6",
          "#91cc75",
          "#fac858",
          "#ee6666",
          "#73c0de",
          "#3ba272",
          "#fc8452"
        ]
      }
    ]
  };

  levelChart.setOption(option);
};

// 更新健康状态分布图表
const updateHealthChart = () => {
  if (!healthChart || !statisticsData.value.health_status_stats) return;

  const statusMap: Record<string, string> = {
    healthy: "健康",
    warning: "警告",
    unhealthy: "不健康",
    no_care: "无人照料"
  };

  const data = Object.entries(statisticsData.value.health_status_stats).map(
    ([key, value]) => ({
      name: statusMap[key] || key,
      value: value as number
    })
  );

  const option = {
    title: {
      text: "健康状态分布",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold"
      }
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      left: "left",
      top: "middle"
    },
    series: [
      {
        name: "健康状态分布",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["60%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: data,
        color: ["#91cc75", "#fac858", "#ee6666", "#73c0de"]
      }
    ]
  };

  healthChart.setOption(option);
};

// 格式化时间（分钟转换为小时分钟）
const formatTime = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return hours > 0 ? `${hours}小时${mins}分钟` : `${mins}分钟`;
};

// 获取健康状态文本
const getHealthStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    healthy: "健康",
    warning: "警告",
    unhealthy: "不健康",
    no_care: "无人照料"
  };
  return statusMap[status] || status;
};

onMounted(async () => {
  await fetchStatistics();
  await nextTick();
  initCharts();

  // 监听窗口大小变化，重新调整图表大小
  window.addEventListener("resize", () => {
    levelChart?.resize();
    healthChart?.resize();
  });
});

onUnmounted(() => {
  // 销毁图表实例
  levelChart?.dispose();
  healthChart?.dispose();

  // 移除事件监听器
  window.removeEventListener("resize", () => {
    levelChart?.resize();
    healthChart?.resize();
  });
});
</script>

<template>
  <div class="main">
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h2 class="text-2xl font-bold text-gray-800 mb-2">成长记录统计</h2>
        <p class="text-gray-600">查看用户成长记录的整体统计数据</p>
      </div>
      <el-button
        type="primary"
        :icon="useRenderIcon('ep:refresh')"
        :loading="loading"
        @click="fetchStatistics"
      >
        刷新数据
      </el-button>
    </div>

    <div
      v-loading="loading"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
    >
      <!-- 总用户数 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
            <i :class="useRenderIcon('ep:user')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">总用户数</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.user_stats?.total_users || 0 }}
            </p>
          </div>
        </div>
      </div>

      <!-- 平均冥想等级 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
            <i :class="useRenderIcon('ep:star')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">平均冥想等级</p>
            <p class="text-2xl font-bold text-gray-800">
              {{
                parseFloat(statisticsData.user_stats?.avg_level || 0).toFixed(1)
              }}
            </p>
          </div>
        </div>
      </div>

      <!-- 总多肉数量 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-orange-100 text-orange-600 mr-4">
            <span class="text-2xl">🌱</span>
          </div>
          <div>
            <p class="text-sm text-gray-600">总多肉数量</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.plant_stats?.total_plants || 0 }}
            </p>
          </div>
        </div>
      </div>

      <!-- 平均多肉等级 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
            <i :class="useRenderIcon('ep:trophy')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">平均多肉等级</p>
            <p class="text-2xl font-bold text-gray-800">
              {{
                parseFloat(statisticsData.plant_stats?.avg_level || 0).toFixed(
                  1
                )
              }}
            </p>
          </div>
        </div>
      </div>

      <!-- 平均能量值 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4">
            <i :class="useRenderIcon('ep:coin')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">平均能量值</p>
            <p class="text-2xl font-bold text-gray-800">
              {{
                parseFloat(statisticsData.plant_stats?.avg_energy || 0).toFixed(
                  0
                )
              }}
            </p>
          </div>
        </div>
      </div>

      <!-- 平均连续天数 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-indigo-100 text-indigo-600 mr-4">
            <i :class="useRenderIcon('ep:timer')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">平均连续天数</p>
            <p class="text-2xl font-bold text-gray-800">
              {{
                parseFloat(
                  statisticsData.user_stats?.avg_streak_days || 0
                ).toFixed(0)
              }}天
            </p>
          </div>
        </div>
      </div>

      <!-- 最大连续天数 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 text-red-600 mr-4">
            <i :class="useRenderIcon('ep:medal')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">最大连续天数</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.user_stats?.max_streak_days || 0 }}天
            </p>
          </div>
        </div>
      </div>

      <!-- 总成长记录数 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-teal-100 text-teal-600 mr-4">
            <i :class="useRenderIcon('ep:document')" class="text-2xl" />
          </div>
          <div>
            <p class="text-sm text-gray-600">总成长记录数</p>
            <p class="text-2xl font-bold text-gray-800">
              {{ statisticsData.growth_record_stats?.total_records || 0 }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 分布统计 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 冥想等级分布图表 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div ref="levelChartRef" v-loading="loading" class="w-full h-80" />
      </div>

      <!-- 健康状态分布图表 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <div ref="healthChartRef" v-loading="loading" class="w-full h-80" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
}
</style>
