import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 用户等级配置数据类型定义
export type UserLevel = {
  id: string;
  level: number;
  level_name: string;
  required_days: number;
  required_duration: number;
  benefits: {
    description: string;
    features: string[];
  };
  icon_url?: string;
  created_at: string;
  updated_at?: string;
};

// 用户等级列表响应类型
export type UserLevelListResponse = {
  code: number;
  message?: string;
  data: UserLevel[];
};

// 用户等级创建/更新响应类型
export type UserLevelResponse = {
  code: number;
  message?: string;
  data: UserLevel;
};

// 删除响应类型
export type DeleteResponse = {
  code: number;
  message?: string;
};

// 创建用户等级参数
export type CreateUserLevelParams = {
  level: number;
  level_name: string;
  required_days: number;
  required_duration: number;
  benefits: {
    description: string;
    features: string[];
  };
  icon_url?: string;
};

// 更新用户等级参数
export type UpdateUserLevelParams = {
  level_name?: string;
  required_days?: number;
  required_duration?: number;
  benefits?: {
    description: string;
    features: string[];
  };
  icon_url?: string;
};

/** 获取用户等级配置列表 */
export const getUserLevelList = () => {
  return http.request<UserLevelListResponse>("get", baseUrlApi("admin/user-levels"));
};

/** 创建用户等级配置 */
export const createUserLevel = (data: CreateUserLevelParams) => {
  return http.request<UserLevelResponse>("post", baseUrlApi("admin/user-levels"), {
    data
  });
};

/** 更新用户等级配置 */
export const updateUserLevel = (id: string, data: UpdateUserLevelParams) => {
  return http.request<UserLevelResponse>("put", baseUrlApi(`admin/user-levels/${id}`), {
    data
  });
};

/** 删除用户等级配置 */
export const deleteUserLevel = (id: string) => {
  return http.request<DeleteResponse>("delete", baseUrlApi(`admin/user-levels/${id}`));
};
