<template>
  <el-dialog
    v-model="visible"
    title="分类详情"
    width="600px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="分类名称">
          {{ data.name }}
        </el-descriptions-item>
        <el-descriptions-item label="类型">
          {{ data.type }}
        </el-descriptions-item>
        <el-descriptions-item label="父级分类">
          {{ data.parentName || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="图标">
          <span style="font-size: 20px;">{{ data.icon }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ data.sort }}
        </el-descriptions-item>
        <el-descriptions-item label="内容数量">
          {{ data.contentCount }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.status === 1 ? 'success' : 'danger'">
            {{ data.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ data.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ data.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface Category {
  id: number;
  name: string;
  type: string;
  parentId: number;
  parentName: string;
  icon: string;
  description: string;
  sort: number;
  contentCount: number;
  status: number;
  createTime: string;
}

interface Props {
  modelValue: boolean;
  data?: Category | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}
</style>
