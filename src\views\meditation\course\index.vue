<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getMeditationContentList,
  createMeditationContent,
  updateCourseRecommend,
  updateCourseStatus,
  deleteMeditationContent,
  getMeditationTagList,
  type MeditationContent,
  type MeditationContentListParams,
  type CreateMeditationContentParams,
  type MeditationTag
} from "@/api/meditation";
import CourseFormDialog from "./modules/CourseFormDialog.vue";
import ChapterManageDialog from "./modules/ChapterManageDialog.vue";

defineOptions({
  name: "MeditationCourse"
});

const loading = ref(false);
const tableData = ref<MeditationContent[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

const searchForm = reactive({
  title: "",
  type: "",
  status: "",
  is_recommended: ""
});

const tags = ref<MeditationTag[]>([]);

// 弹窗状态
const courseFormDialogVisible = ref(false);
const chapterManageDialogVisible = ref(false);
const editingCourseId = ref("");

const typeOptions = [
  { label: "冥想", value: "meditation" },
  { label: "声音", value: "sound" },
  { label: "睡眠", value: "sleep" }
];

const statusOptions = [
  { label: "已发布", value: "published" },
  { label: "已下架", value: "unpublished" }
];

const recommendOptions = [
  { label: "推荐", value: true },
  { label: "普通", value: false }
];

const columns = [
  {
    label: "课程封面",
    slot: "cover",
    minWidth: 100
  },
  {
    label: "课程标题",
    prop: "title",
    minWidth: 200
  },
  {
    label: "类型",
    prop: "type",
    minWidth: 80
  },
  {
    label: "描述",
    prop: "description",
    minWidth: 200
  },
  {
    label: "时长",
    prop: "duration",
    minWidth: 100
  },
  {
    label: "标签",
    prop: "tags_text",
    minWidth: 150
  },
  {
    label: "收藏数",
    prop: "favorite_count",
    minWidth: 100
  },
  {
    label: "推荐",
    prop: "is_recommended",
    minWidth: 80
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 280,
    slot: "operation"
  }
];

const onSearch = async () => {
  loading.value = true;
  try {
    const params: MeditationContentListParams = {
      page: pagination.currentPage,
      limit: pagination.pageSize,
      sub_type: "course"
    };

    if (searchForm.title) {
      params.search = searchForm.title;
    }
    if (searchForm.type) {
      params.type = searchForm.type as "meditation" | "sound" | "sleep";
    }
    if (searchForm.status) {
      params.status = searchForm.status as "published" | "unpublished";
    }
    if (searchForm.is_recommended !== "") {
      params.is_recommended = searchForm.is_recommended === "true";
    }

    const { data } = await getMeditationContentList(params);
    tableData.value = data.items;
    pagination.total = data.total;
  } catch (error) {
    ElMessage.error("获取课程列表失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  searchForm.title = "";
  searchForm.type = "";
  searchForm.status = "";
  searchForm.is_recommended = "";
  onSearch();
};

// 加载标签列表
const loadTags = async () => {
  try {
    const { data } = await getMeditationTagList();
    tags.value = data.items;
  } catch (error) {
    console.error("获取标签列表失败:", error);
  }
};

const handleAdd = () => {
  editingCourseId.value = "";
  courseFormDialogVisible.value = true;
};

const handleEdit = (row: MeditationContent) => {
  editingCourseId.value = row.id;
  courseFormDialogVisible.value = true;
};

const handleManageLessons = (row: MeditationContent) => {
  editingCourseId.value = row.id;
  chapterManageDialogVisible.value = true;
};

const handleToggleRecommend = async (row: MeditationContent) => {
  const action = row.is_recommended ? "取消推荐" : "设为推荐";
  try {
    await ElMessageBox.confirm(
      `确定要${action}课程 ${row.title} 吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    await updateCourseRecommend(row.id, {
      is_recommended: !row.is_recommended
    });

    row.is_recommended = !row.is_recommended;
    ElMessage.success(`${action}成功`);
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error(`${action}失败`);
      console.error(error);
    }
  }
};

const handleToggleStatus = async (row: MeditationContent) => {
  let action = "";
  let newStatus: "published" | "unpublished" = "published";

  if (row.status === "published") {
    action = "下架";
    newStatus = "unpublished";
  } else {
    action = "上架";
    newStatus = "published";
  }

  try {
    await ElMessageBox.confirm(
      `确定要${action}课程 ${row.title} 吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    await updateCourseStatus(row.id, { status: newStatus });
    row.status = newStatus;
    ElMessage.success(`${action}成功`);
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error(`${action}失败`);
      console.error(error);
    }
  }
};

const handleDelete = async (row: MeditationContent) => {
  try {
    await ElMessageBox.confirm(`确定要删除课程 ${row.title} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    await deleteMeditationContent(row.id);
    ElMessage.success("删除成功");
    onSearch(); // 重新加载列表
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
      console.error(error);
    }
  }
};

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return hours > 0 ? `${hours}h${mins}m` : `${mins}m`;
};

const getStatusTag = (status: string) => {
  const statusMap = {
    published: { type: "success", text: "已发布" },
    unpublished: { type: "warning", text: "已下架" }
  };
  return statusMap[status] || { type: "info", text: "未知" };
};

const getTypeText = (type: string) => {
  const typeMap = {
    meditation: "冥想",
    sound: "声音",
    sleep: "睡眠"
  };
  return typeMap[type] || type;
};

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString("zh-CN");
};

onMounted(() => {
  loadTags();
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="课程标题：" prop="title">
        <el-input
          v-model="searchForm.title"
          placeholder="请输入课程标题"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="类型：" prop="type">
        <el-select
          v-model="searchForm.type"
          placeholder="请选择类型"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推荐：" prop="is_recommended">
        <el-select
          v-model="searchForm.is_recommended"
          placeholder="请选择推荐状态"
          clearable
          class="!w-[140px]"
        >
          <el-option
            v-for="item in recommendOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="课程管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增课程
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #cover="{ row }">
            <el-image
              v-if="row.cover_url"
              :src="row.cover_url"
              style="width: 80px; height: 80px"
              fit="cover"
              :preview-src-list="[row.cover_url]"
            />
            <span v-else class="text-gray-400">无封面</span>
          </template>

          <template #type="{ row }">
            <el-tag>{{ getTypeText(row.type) }}</el-tag>
          </template>

          <template #description="{ row }">
            <el-tooltip :content="row.description" placement="top">
              <span class="truncate max-w-[200px] block">
                {{ row.description }}
              </span>
            </el-tooltip>
          </template>

          <template #duration="{ row }">
            <span>{{ formatDuration(row.duration) }}</span>
          </template>

          <template #tags_text="{ row }">
            <el-tooltip :content="row.tags_text" placement="top">
              <span class="truncate max-w-[150px] block">
                {{ row.tags_text }}
              </span>
            </el-tooltip>
          </template>

          <template #is_recommended="{ row }">
            <el-tag :type="row.is_recommended ? 'success' : 'info'">
              {{ row.is_recommended ? "推荐" : "普通" }}
            </el-tag>
          </template>

          <template #status="{ row }">
            <el-tag :type="getStatusTag(row.status).type">
              {{ getStatusTag(row.status).text }}
            </el-tag>
          </template>

          <template #created_at="{ row }">
            <span>{{ formatDate(row.created_at) }}</span>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:list')"
              @click="handleManageLessons(row)"
            >
              章节
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.is_recommended ? 'warning' : 'success'"
              :size="size"
              :icon="
                useRenderIcon(row.is_recommended ? 'ep:star-filled' : 'ep:star')
              "
              @click="handleToggleRecommend(row)"
            >
              {{ row.is_recommended ? "取消推荐" : "推荐" }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.status === 'published' ? 'warning' : 'success'"
              :size="size"
              :icon="
                useRenderIcon(
                  row.status === 'published' ? 'ep:bottom' : 'ep:top'
                )
              "
              @click="handleToggleStatus(row)"
            >
              {{ row.status === "published" ? "下架" : "上架" }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 课程表单弹窗 -->
    <CourseFormDialog
      v-model="courseFormDialogVisible"
      :course-id="editingCourseId"
      :all-tags="tags"
      @success="onSearch"
    />

    <!-- 章节管理弹窗 -->
    <ChapterManageDialog
      v-model="chapterManageDialogVisible"
      :course-id="editingCourseId"
      :all-tags="tags"
    />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
