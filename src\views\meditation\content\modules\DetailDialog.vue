<template>
  <el-dialog
    v-model="visible"
    title="内容详情"
    width="800px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="标题" :span="2">
          {{ data.title }}
        </el-descriptions-item>
        <el-descriptions-item label="类型">
          {{ getTypeLabel(data.type) }}
        </el-descriptions-item>
        <el-descriptions-item label="子类型">
          {{ getSubTypeLabel(data.sub_type) }}
        </el-descriptions-item>
        <el-descriptions-item label="时长">
          {{ formatDuration(data.duration) }}
        </el-descriptions-item>
        <el-descriptions-item label="收藏数">
          {{ data.favorite_count }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTag(data.status).type">
            {{ getStatusTag(data.status).text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="是否推荐">
          <el-tag :type="data.is_recommended ? 'success' : 'info'">
            {{ data.is_recommended ? "是" : "否" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ data.sort_order }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(data.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDateTime(data.updated_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="封面" :span="2">
          <el-image
            v-if="data.cover_url"
            :src="data.cover_url"
            fit="cover"
            style="width: 200px; height: 120px; border-radius: 8px"
            :preview-src-list="[data.cover_url]"
          />
          <span v-else class="text-gray-400">无封面</span>
        </el-descriptions-item>
        <el-descriptions-item label="标签" :span="2">
          <el-tag
            v-for="tag in data.tags"
            :key="tag.id"
            size="small"
            style="margin-right: 8px"
          >
            {{ tag.name }}
          </el-tag>
          <span
            v-if="!data.tags || data.tags.length === 0"
            class="text-gray-400"
          >
            无标签
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ data.description || "暂无描述" }}
        </el-descriptions-item>
        <el-descriptions-item v-if="data.audio_url" label="音频链接" :span="2">
          <el-link :href="data.audio_url" target="_blank" type="primary">
            {{ data.audio_url }}
          </el-link>
        </el-descriptions-item>
        <el-descriptions-item v-if="data.video_url" label="视频链接" :span="2">
          <el-link :href="data.video_url" target="_blank" type="primary">
            {{ data.video_url }}
          </el-link>
        </el-descriptions-item>
        <el-descriptions-item v-if="data.parent" label="父级内容" :span="2">
          {{ data.parent.title }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { MeditationContent, MeditationTag } from "@/api/meditation";

interface Props {
  modelValue: boolean;
  data?: MeditationContent | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

// 格式化函数
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const formatDateTime = (dateString: string) => {
  if (!dateString) return "--";
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};

const getTypeLabel = (type: string) => {
  const typeMap = {
    meditation: "冥想",
    sound: "声音",
    sleep: "睡眠"
  };
  return typeMap[type] || type;
};

const getSubTypeLabel = (subType: string) => {
  if (!subType) return "--";
  const subTypeMap = {
    course: "课程",
    single: "单节"
  };
  return subTypeMap[subType] || subType;
};

const getStatusTag = (status: string) => {
  const statusMap = {
    published: { type: "success", text: "已上架" },
    unpublished: { type: "warning", text: "已下架" }
  };
  return statusMap[status] || { type: "info", text: "未知" };
};

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, val => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}
</style>
