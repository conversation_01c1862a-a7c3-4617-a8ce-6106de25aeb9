import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 多肉品种数据类型定义
export type PlantSpecies = {
  id: string;
  name: string;
  display_name: string;
  description: string;
  rarity: "common" | "rare" | "epic" | "legendary";
  unlock_condition: {
    level: number;
    days: number;
    special_achievement?: string;
  };
  growth_stages: {
    stages: Array<{
      level: number;
      name: string;
    }>;
  };
  max_level: number;
  base_energy_per_level: number;
  image_urls: Record<string, string>;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
};

// 多肉品种列表响应类型
export type PlantSpeciesListResponse = {
  code: number;
  message?: string;
  data: {
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    items: PlantSpecies[];
  };
};

// 多肉品种详情响应类型
export type PlantSpeciesResponse = {
  code: number;
  message?: string;
  data: PlantSpecies;
};

// 通用删除响应类型
export type DeleteResponse = {
  code: number;
  message?: string;
};

// 多肉品种列表查询参数
export type PlantSpeciesListParams = {
  page?: number;
  limit?: number;
  search?: string;
  rarity?: "common" | "rare" | "epic" | "legendary";
};

// 多肉品种创建参数
export type CreatePlantSpeciesParams = {
  name: string;
  display_name: string;
  description: string;
  rarity: "common" | "rare" | "epic" | "legendary";
  unlock_condition: {
    level: number;
    days: number;
    special_achievement?: string;
  };
  growth_stages: {
    stages: Array<{
      level: number;
      name: string;
    }>;
  };
  max_level: number;
  base_energy_per_level: number;
  image_urls: Record<string, string>;
};

// 多肉品种更新参数
export type UpdatePlantSpeciesParams = {
  display_name?: string;
  description?: string;
  rarity?: "common" | "rare" | "epic" | "legendary";
  unlock_condition?: {
    level: number;
    days: number;
    special_achievement?: string;
  };
  growth_stages?: {
    stages: Array<{
      level: number;
      name: string;
    }>;
  };
  max_level?: number;
  base_energy_per_level?: number;
  image_urls?: Record<string, string>;
  is_active?: boolean;
};

// ==================== 多肉品种管理接口 ====================

/** 获取多肉品种列表（管理端） */
export const getPlantSpeciesList = (params?: PlantSpeciesListParams) => {
  return http.request<PlantSpeciesListResponse>("get", baseUrlApi("admin/plants/species"), {
    params
  });
};

/** 创建多肉品种 */
export const createPlantSpecies = (data: CreatePlantSpeciesParams) => {
  return http.request<PlantSpeciesResponse>("post", baseUrlApi("admin/plants/species"), {
    data
  });
};

/** 获取多肉品种详情（管理端） */
export const getPlantSpeciesDetail = (id: string) => {
  return http.request<PlantSpeciesResponse>("get", baseUrlApi(`admin/plants/species/${id}`));
};

/** 更新多肉品种 */
export const updatePlantSpecies = (id: string, data: UpdatePlantSpeciesParams) => {
  return http.request<PlantSpeciesResponse>("put", baseUrlApi(`admin/plants/species/${id}`), {
    data
  });
};

/** 删除多肉品种 */
export const deletePlantSpecies = (id: string) => {
  return http.request<DeleteResponse>("delete", baseUrlApi(`admin/plants/species/${id}`));
};
