<template>
  <el-dialog
    v-model="visible"
    title="品种详情"
    width="800px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="品种名称">
          {{ data.name }}
        </el-descriptions-item>
        <el-descriptions-item label="学名">
          {{ data.scientificName }}
        </el-descriptions-item>
        <el-descriptions-item label="科属">
          {{ data.category }}
        </el-descriptions-item>
        <el-descriptions-item label="稀有度">
          <el-tag 
            :type="data.rarity === '稀有' ? 'warning' : data.rarity === '史诗' ? 'danger' : 'success'"
          >
            {{ data.rarity }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="成长周期">
          {{ data.growthCycle }}天
        </el-descriptions-item>
        <el-descriptions-item label="最大等级">
          {{ data.maxLevel }}级
        </el-descriptions-item>
        <el-descriptions-item label="基础能量">
          {{ data.baseEnergy }}
        </el-descriptions-item>
        <el-descriptions-item label="解锁条件">
          {{ data.unlockCondition }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.status === 1 ? 'success' : 'danger'">
            {{ data.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ data.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="图片" :span="2">
          <el-image
            :src="data.image"
            fit="cover"
            style="width: 200px; height: 200px; border-radius: 8px;"
            :preview-src-list="[data.image]"
          />
        </el-descriptions-item>
        <el-descriptions-item label="属性" :span="2">
          <div class="attributes">
            <div class="attribute-item">
              <span class="attribute-label">美观:</span>
              <el-progress 
                :percentage="data.attributes.beauty" 
                :stroke-width="8"
                :show-text="true"
                color="#f56c6c"
              />
            </div>
            <div class="attribute-item">
              <span class="attribute-label">耐性:</span>
              <el-progress 
                :percentage="data.attributes.hardiness" 
                :stroke-width="8"
                :show-text="true"
                color="#67c23a"
              />
            </div>
            <div class="attribute-item">
              <span class="attribute-label">成长:</span>
              <el-progress 
                :percentage="data.attributes.growth" 
                :stroke-width="8"
                :show-text="true"
                color="#409eff"
              />
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ data.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface Variety {
  id: number;
  name: string;
  scientificName: string;
  category: string;
  rarity: string;
  image: string;
  description: string;
  growthCycle: number;
  maxLevel: number;
  baseEnergy: number;
  unlockCondition: string;
  attributes: {
    beauty: number;
    hardiness: number;
    growth: number;
  };
  status: number;
  createTime: string;
}

interface Props {
  modelValue: boolean;
  data?: Variety | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}

.attributes {
  .attribute-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    .attribute-label {
      width: 60px;
      font-weight: 500;
      margin-right: 12px;
    }
    
    :deep(.el-progress) {
      flex: 1;
    }
  }
}
</style>
