<template>
  <el-dialog
    v-model="visible"
    title="调整章节顺序"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="space-y-2" v-loading="loading">
      <div
        v-for="(chapter, index) in reorderList"
        :key="chapter.id"
        class="flex items-center justify-between p-3 border rounded hover:bg-gray-50"
      >
        <div class="flex-1">
          <div class="font-medium">{{ chapter.title }}</div>
          <div class="text-sm text-gray-500 truncate">{{ chapter.description }}</div>
        </div>
        <div class="flex items-center space-x-2 ml-4">
          <el-input-number
            v-model="chapter.sort_order"
            :min="1"
            :max="reorderList.length"
            size="small"
            style="width: 80px"
            @change="handleOrderChange"
          />
          <el-button
            size="small"
            :icon="useRenderIcon('ep:top')"
            @click="moveUp(index)"
            :disabled="index === 0"
            title="上移"
          />
          <el-button
            size="small"
            :icon="useRenderIcon('ep:bottom')"
            @click="moveDown(index)"
            :disabled="index === reorderList.length - 1"
            title="下移"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSaveReorder" :loading="saving">
        保存顺序
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  reorderCourseChapters,
  type CourseChapter,
  type ReorderChaptersParams
} from "@/api/meditation";

interface Props {
  modelValue: boolean;
  courseId: string;
  chapters: CourseChapter[];
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  chapters: () => []
});

const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const saving = ref(false);
const reorderList = ref<CourseChapter[]>([]);

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal;
    if (newVal) {
      initReorderList();
    }
  },
  { immediate: true }
);

// 监听内部visible状态
watch(visible, (newVal) => {
  emit("update:modelValue", newVal);
});

// 初始化排序列表
const initReorderList = () => {
  reorderList.value = [...props.chapters].sort((a, b) => a.sort_order - b.sort_order);
};

// 处理排序号变化
const handleOrderChange = () => {
  // 根据排序号重新排序
  reorderList.value.sort((a, b) => a.sort_order - b.sort_order);
  
  // 重新分配连续的排序号
  reorderList.value.forEach((item, index) => {
    item.sort_order = index + 1;
  });
};

// 上移
const moveUp = (index: number) => {
  if (index > 0) {
    const temp = reorderList.value[index];
    reorderList.value[index] = reorderList.value[index - 1];
    reorderList.value[index - 1] = temp;
    
    // 更新排序号
    updateSortOrders();
  }
};

// 下移
const moveDown = (index: number) => {
  if (index < reorderList.value.length - 1) {
    const temp = reorderList.value[index];
    reorderList.value[index] = reorderList.value[index + 1];
    reorderList.value[index + 1] = temp;
    
    // 更新排序号
    updateSortOrders();
  }
};

// 更新排序号
const updateSortOrders = () => {
  reorderList.value.forEach((item, index) => {
    item.sort_order = index + 1;
  });
};

// 保存顺序
const handleSaveReorder = async () => {
  try {
    saving.value = true;
    const reorderData: ReorderChaptersParams = {
      chapter_orders: reorderList.value.map(chapter => ({
        id: parseInt(chapter.id),
        sort_order: chapter.sort_order
      }))
    };

    await reorderCourseChapters(props.courseId, reorderData);
    ElMessage.success("调整顺序成功");
    handleClose();
    emit("success");
  } catch (error) {
    ElMessage.error("调整顺序失败");
    console.error(error);
  } finally {
    saving.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.space-y-2 > * + * {
  margin-top: 0.5rem;
}
</style>
