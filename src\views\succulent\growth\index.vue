<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getGrowthRecordList,
  type GrowthRecord,
  type GrowthRecordListParams
} from "@/api/growth-records";
import EditDialog from "./modules/edit-dialog.vue";

defineOptions({
  name: "SucculentGrowth"
});

const loading = ref(false);
const tableData = ref<GrowthRecord[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

const searchForm = reactive({
  search: "",
  level: "",
  plant_level: ""
});

// 编辑弹窗相关
const editDialogVisible = ref(false);
const editDialogType = ref<"level" | "energy">("level");
const currentEditData = ref<GrowthRecord | null>(null);
const currentPlantData = ref<any>(null);

const levelOptions = [
  { label: "1级", value: 1 },
  { label: "2级", value: 2 },
  { label: "3级", value: 3 },
  { label: "4级", value: 4 },
  { label: "5级", value: 5 }
];

const plantLevelOptions = [
  { label: "1级", value: 1 },
  { label: "2级", value: 2 },
  { label: "3级", value: 3 },
  { label: "4级", value: 4 },
  { label: "5级", value: 5 }
];

const healthStatusOptions = [
  { label: "健康", value: "healthy" },
  { label: "优秀", value: "excellent" },
  { label: "不健康", value: "unhealthy" }
];

const columns = [
  {
    label: "用户信息",
    prop: "nickname",
    minWidth: 150
  },
  {
    label: "多肉信息",
    prop: "plants",
    minWidth: 200
  },
  {
    label: "冥想等级",
    prop: "meditation_level",
    minWidth: 100
  },
  {
    label: "连续天数",
    prop: "streak_days",
    minWidth: 100
  },
  {
    label: "成长天数",
    prop: "growth_days",
    minWidth: 100
  },
  {
    label: "总能量",
    prop: "total_energy",
    minWidth: 100
  },
  {
    label: "健康状态",
    prop: "health_status",
    minWidth: 100
  },
  {
    label: "成就数量",
    prop: "achievements",
    minWidth: 100
  },
  {
    label: "操作",
    fixed: "right",
    width: 180,
    slot: "operation"
  }
];

// 获取成长记录列表
const fetchGrowthRecords = async () => {
  try {
    loading.value = true;
    const params: GrowthRecordListParams = {
      page: pagination.currentPage,
      limit: pagination.pageSize,
      search: searchForm.search || undefined,
      level: searchForm.level || undefined,
      plant_level: searchForm.plant_level || undefined
    };

    const response = await getGrowthRecordList(params);
    if (response.code === 200) {
      tableData.value = response.data.items;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.message || "获取数据失败");
    }
  } catch (error) {
    console.error("获取成长记录失败:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

const onSearch = () => {
  pagination.currentPage = 1;
  fetchGrowthRecords();
};

const resetForm = () => {
  searchForm.search = "";
  searchForm.level = "";
  searchForm.plant_level = "";
  onSearch();
};

// 分页变化处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  fetchGrowthRecords();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  fetchGrowthRecords();
};

const handleViewDetail = (row: GrowthRecord) => {
  // 跳转到详情页面
  window.open(`/succulent/growth/${row.id}/detail`, "_blank");
};

// 更新用户等级
const handleUpdateLevel = (row: GrowthRecord) => {
  currentEditData.value = row;
  editDialogType.value = "level";
  editDialogVisible.value = true;
};

// 更新多肉能量
const handleUpdateEnergy = (row: GrowthRecord, plant: any) => {
  currentEditData.value = row;
  currentPlantData.value = plant;
  editDialogType.value = "energy";
  editDialogVisible.value = true;
};

// 编辑成功回调
const handleEditSuccess = () => {
  fetchGrowthRecords(); // 刷新列表
};

const getHealthStatusTag = (status: string) => {
  const statusMap = {
    healthy: { type: "success", color: "#67C23A", text: "健康" },
    excellent: { type: "primary", color: "#409EFF", text: "优秀" },
    unhealthy: { type: "danger", color: "#F56C6C", text: "不健康" }
  };
  return statusMap[status] || { type: "info", color: "#909399", text: "未知" };
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleString("zh-CN");
};

// 获取主要多肉信息
const getPrimaryPlant = (plants: any[]) => {
  return plants && plants.length > 0 ? plants[0] : null;
};

onMounted(() => {
  fetchGrowthRecords();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="搜索：" prop="search">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入用户昵称或ID"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="冥想等级：" prop="level">
        <el-select
          v-model="searchForm.level"
          placeholder="请选择等级"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in levelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="多肉等级：" prop="plant_level">
        <el-select
          v-model="searchForm.plant_level"
          placeholder="请选择多肉等级"
          clearable
          class="!w-[140px]"
        >
          <el-option
            v-for="item in plantLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="多肉成长记录" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button type="success" :icon="useRenderIcon('ep:download')">
          导出数据
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #user="{ row }">
            <div class="flex items-center">
              <el-avatar :size="40" :src="row.avatar_url" class="mr-3" />
              <div>
                <div class="font-medium">{{ row.nickname }}</div>
                <div class="text-sm text-gray-500">ID: {{ row.id }}</div>
              </div>
            </div>
          </template>

          <template #plants="{ row }">
            <div v-if="getPrimaryPlant(row.plants)" class="flex items-center">
              <div
                class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3"
              >
                <span class="text-green-600 text-xl">🌱</span>
              </div>
              <div>
                <div class="font-medium">
                  {{ getPrimaryPlant(row.plants).species }}
                </div>
                <div class="text-sm text-gray-500">
                  等级: {{ getPrimaryPlant(row.plants).level }}
                </div>
                <div class="text-xs text-gray-400">
                  能量: {{ getPrimaryPlant(row.plants).energy_value }}
                </div>
              </div>
            </div>
            <div v-else class="text-gray-400">暂无多肉</div>
          </template>

          <template #meditation_level="{ row }">
            <div class="text-center">
              <div class="font-medium text-lg">
                Lv.{{ row.meditation_level }}
              </div>
            </div>
          </template>

          <template #streak_days="{ row }">
            <div class="text-center">
              <div class="font-medium">{{ row.streak_days }}</div>
              <div class="text-xs text-gray-500">天</div>
            </div>
          </template>

          <template #growth_days="{ row }">
            <div class="text-center">
              <div class="font-medium">{{ row.growth_days }}</div>
              <div class="text-xs text-gray-500">天</div>
            </div>
          </template>

          <template #total_energy="{ row }">
            <div class="text-center">
              <div class="font-medium text-orange-600">
                {{ row.total_energy }}
              </div>
            </div>
          </template>

          <template #health_status="{ row }">
            <el-tag :type="getHealthStatusTag(row.health_status).type">
              {{ getHealthStatusTag(row.health_status).text }}
            </el-tag>
          </template>

          <template #achievements="{ row }">
            <div class="text-center">
              <div class="font-medium">{{ row.achievements.length }}</div>
              <div class="text-xs text-gray-500">个成就</div>
            </div>
          </template>

          <template #operation="{ row }">
            <div class="flex flex-col gap-1">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon('ep:view')"
                @click="handleViewDetail(row)"
              >
                详情
              </el-button>
              <el-button
                class="reset-margin"
                link
                type="warning"
                :size="size"
                :icon="useRenderIcon('ep:edit')"
                @click="handleUpdateLevel(row)"
              >
                调整等级
              </el-button>
              <el-button
                v-if="getPrimaryPlant(row.plants)"
                class="reset-margin"
                link
                type="success"
                :size="size"
                :icon="useRenderIcon('ep:coin')"
                @click="handleUpdateEnergy(row, getPrimaryPlant(row.plants))"
              >
                调整能量
              </el-button>
            </div>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 编辑弹窗 -->
    <EditDialog
      v-model:visible="editDialogVisible"
      :data="currentEditData"
      :type="editDialogType"
      :plant-data="currentPlantData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
