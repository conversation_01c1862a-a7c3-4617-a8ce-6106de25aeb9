<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";
import EditDialog from "./modules/EditDialog.vue";
import {
  getMeditationContentList,
  createMeditationContent,
  updateMeditationContent,
  deleteMeditationContent,
  publishMeditationContent,
  unpublishMeditationContent,
  getMeditationTagList,
  getMeditationContentDetail,
  type MeditationContent,
  type MeditationContentListParams,
  type CreateMeditationContentParams,
  type UpdateMeditationContentParams
} from "@/api/meditation";

defineOptions({
  name: "MeditationContent"
});

const loading = ref(false);
const tableData = ref<MeditationContent[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});
const detailDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentRow = ref<MeditationContent | null>(null);

const searchForm = reactive({
  search: "",
  type: "" as "" | "meditation" | "sound" | "sleep",
  sub_type: "" as "" | "course" | "single",
  status: "" as "" | "published" | "unpublished"
});

// 选项配置
const typeOptions = [
  { label: "冥想", value: "meditation" },
  { label: "声音", value: "sound" },
  { label: "睡眠", value: "sleep" }
];

const subTypeOptions = [
  { label: "课程", value: "course" },
  { label: "单节", value: "single" }
];

const statusOptions = [
  { label: "已上架", value: "published" },
  { label: "已下架", value: "unpublished" }
];

const columns = [
  {
    label: "封面",
    slot: "cover",
    minWidth: 80
  },
  {
    label: "标题",
    prop: "title",
    minWidth: 200
  },
  {
    label: "类型",
    prop: "type",
    slot: "type",
    minWidth: 80
  },
  {
    label: "子类型",
    prop: "sub_type",
    slot: "sub_type",
    minWidth: 80
  },
  {
    label: "标签",
    prop: "tags_text",
    slot: "tags_text",
    minWidth: 150
  },
  {
    label: "时长",
    prop: "duration",
    slot: "duration",
    minWidth: 80
  },
  {
    label: "收藏数",
    prop: "favorite_count",
    minWidth: 80
  },
  {
    label: "状态",
    prop: "status",
    slot: "status",
    minWidth: 80
  },
  {
    label: "创建时间",
    prop: "created_at",
    slot: "created_at",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 240,
    slot: "operation"
  }
];

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    const params: MeditationContentListParams = {
      page: pagination.currentPage,
      limit: pagination.pageSize,
      search: searchForm.search || undefined,
      type: searchForm.type || undefined,
      sub_type: searchForm.sub_type || undefined,
      status: searchForm.status || undefined
    };

    const response = await getMeditationContentList(params);
    if (response.code === 200) {
      tableData.value = response.data.items;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.message || "获取数据失败");
    }
  } catch (error) {
    console.error("获取冥想内容列表失败:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

const onSearch = () => {
  pagination.currentPage = 1;
  fetchData();
};

const resetForm = () => {
  searchForm.search = "";
  searchForm.type = "";
  searchForm.sub_type = "";
  searchForm.status = "";
  onSearch();
};

const handleAdd = () => {
  currentRow.value = null;
  editDialogVisible.value = true;
};

const handleEdit = async (row: MeditationContent) => {
  try {
    const response = await getMeditationContentDetail(row.id);
    if (response.code === 200) {
      currentRow.value = response.data;
      editDialogVisible.value = true;
    } else {
      ElMessage.error(response.message || "获取详情失败");
    }
  } catch (error) {
    console.error("获取冥想内容详情失败:", error);
    ElMessage.error("获取详情失败");
  }
};

const handleDelete = async (row: MeditationContent) => {
  try {
    await ElMessageBox.confirm(`确定要删除内容 ${row.title} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    const response = await deleteMeditationContent(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      fetchData();
    } else {
      ElMessage.error(response.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除冥想内容失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

const handleDetail = async (row: MeditationContent) => {
  try {
    const response = await getMeditationContentDetail(row.id);
    if (response.code === 200) {
      currentRow.value = response.data;
      detailDialogVisible.value = true;
    } else {
      ElMessage.error(response.message || "获取详情失败");
    }
  } catch (error) {
    console.error("获取冥想内容详情失败:", error);
    ElMessage.error("获取详情失败");
  }
};

const handlePreview = (row: MeditationContent) => {
  ElMessage.info(`预览内容: ${row.title}`);
};

// 上架内容
const handlePublish = async (row: MeditationContent) => {
  try {
    await ElMessageBox.confirm(`确定要上架内容 ${row.title} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "success"
    });

    const response = await publishMeditationContent(row.id);
    if (response.code === 200) {
      ElMessage.success("上架成功");
      fetchData();
    } else {
      ElMessage.error(response.message || "上架失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("上架内容失败:", error);
      ElMessage.error("上架失败");
    }
  }
};

// 下架内容
const handleUnpublish = async (row: MeditationContent) => {
  try {
    await ElMessageBox.confirm(`确定要下架内容 ${row.title} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    const response = await unpublishMeditationContent(row.id);
    if (response.code === 200) {
      ElMessage.success("下架成功");
      fetchData();
    } else {
      ElMessage.error(response.message || "下架失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("下架内容失败:", error);
      ElMessage.error("下架失败");
    }
  }
};

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const formatDateTime = (dateString: string) => {
  if (!dateString) return "--";
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};

const getStatusTag = (status: string) => {
  const statusMap = {
    published: { type: "success", text: "已上架" },
    unpublished: { type: "warning", text: "已下架" }
  };
  return statusMap[status] || { type: "info", text: "未知" };
};

const getTypeLabel = (type: string) => {
  const typeMap = {
    meditation: "冥想",
    sound: "声音",
    sleep: "睡眠"
  };
  return typeMap[type] || type;
};

const getSubTypeLabel = (subType: string) => {
  if (!subType) return "--";
  const subTypeMap = {
    course: "课程",
    single: "单节"
  };
  return subTypeMap[subType] || subType;
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  fetchData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  fetchData();
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="搜索：" prop="search">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入内容标题"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="类型：" prop="type">
        <el-select
          v-model="searchForm.type"
          placeholder="请选择类型"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="子类型：" prop="sub_type">
        <el-select
          v-model="searchForm.sub_type"
          placeholder="请选择子类型"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in subTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="冥想内容管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增内容
        </el-button>
        <el-button type="success" :icon="useRenderIcon('ep:upload')">
          批量导入
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <template #cover="{ row }">
            <el-image
              v-if="row.cover_url"
              :src="row.cover_url"
              style="width: 60px; height: 60px"
              fit="cover"
              :preview-src-list="[row.cover_url]"
            />
            <span v-else class="text-gray-400">无封面</span>
          </template>

          <template #type="{ row }">
            {{ getTypeLabel(row.type) }}
          </template>

          <template #sub_type="{ row }">
            {{ getSubTypeLabel(row.sub_type) }}
          </template>

          <template #tags_text="{ row }">
            <el-tag
              v-for="tag in row.tags"
              :key="tag.id"
              size="small"
              class="mr-1"
            >
              {{ tag.name }}
            </el-tag>
          </template>

          <template #duration="{ row }">
            {{ formatDuration(row.duration) }}
          </template>

          <template #status="{ row }">
            <el-tag :type="getStatusTag(row.status).type">
              {{ getStatusTag(row.status).text }}
            </el-tag>
          </template>

          <template #created_at="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.status === 'unpublished'"
              class="reset-margin"
              link
              type="success"
              :size="size"
              :icon="useRenderIcon('ep:top')"
              @click="handlePublish(row)"
            >
              上架
            </el-button>
            <el-button
              v-if="row.status === 'published'"
              class="reset-margin"
              link
              type="warning"
              :size="size"
              :icon="useRenderIcon('ep:bottom')"
              @click="handleUnpublish(row)"
            >
              下架
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />

    <!-- 编辑对话框 -->
    <EditDialog
      v-model="editDialogVisible"
      :data="currentRow"
      @success="fetchData"
    />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

.el-tag {
  margin-right: 4px;
}
</style>
